/**
 * 确认替换器
 * 提供快速替换原生 confirm() 的功能
 */

import { autoConfirm } from './autoConfirmController';

/**
 * 增强的确认函数，支持自动确认
 * @param {string} message - 确认消息
 * @param {Object} options - 选项配置
 * @returns {Promise<boolean>|boolean} - 确认结果
 */
export function smartConfirm(message, options = {}) {
  const { async = true, ...otherOptions } = options;

  if (async) {
    // 异步版本，返回 Promise
    return autoConfirm(message, otherOptions);
  } else {
    // 同步版本，直接返回结果（用于兼容现有代码）
    const controller = window.autoConfirmController;
    if (controller && controller.isEnabled) {
      // 如果启用了自动确认，直接返回默认行为
      return controller.defaultBehavior;
    }
    // 否则使用原生 confirm
    return window.confirm(message);
  }
}

/**
 * 替换特定文件中的 confirm() 调用
 * 这个函数可以帮助快速修改现有代码
 */
export function replaceConfirmInCode() {
  const instructions = `
// 要替换现有的 confirm() 调用，按以下步骤操作：

// 1. 在文件顶部导入
import { smartConfirm } from '../utils/confirmReplacer';

// 2. 将同步调用替换为异步调用：

// 原代码：
// if (!confirm('确定要删除吗？')) {
//   return;
// }

// 替换为：
// const confirmed = await smartConfirm('确定要删除吗？');
// if (!confirmed) {
//   return;
// }

// 或者使用同步版本（兼容现有代码结构）：
// if (!smartConfirm('确定要删除吗？', { async: false })) {
//   return;
// }

// 3. 如果是在非 async 函数中，需要将函数改为 async，或使用同步版本
`;

  // Instructions for replacing confirm() calls
  return instructions;
}

/**
 * 为特定操作类型提供预设配置
 */
export const confirmPresets = {
  delete: {
    delay: 800,
    behavior: true, // 删除操作默认自动确认
    showLog: true
  },

  batchDelete: {
    delay: 1500,
    behavior: false, // 批量删除默认需要手动确认
    showLog: true
  },

  revokePermission: {
    delay: 1000,
    behavior: true,
    showLog: true
  },

  dataSubmission: {
    delay: 300,
    behavior: true,
    showLog: true
  },

  irreversible: {
    delay: 2000,
    behavior: false, // 不可逆操作默认需要手动确认
    showLog: true
  }
};

/**
 * 带预设的确认函数
 * @param {string} message - 确认消息
 * @param {string} preset - 预设类型
 * @param {Object} overrides - 覆盖选项
 * @returns {Promise<boolean>} - 确认结果
 */
export function confirmWithPreset(message, preset = 'default', overrides = {}) {
  const presetOptions = confirmPresets[preset] || {};
  const options = { ...presetOptions, ...overrides };
  return smartConfirm(message, options);
}

/**
 * 快速启用/禁用自动确认的工具函数
 */
export function quickToggle() {
  const controller = window.autoConfirmController;
  if (controller) {
    controller.toggle();
    return controller.isEnabled;
  }
  // 自动确认控制器未找到
  return false;
}

/**
 * 一键设置常用配置
 */
export const quickSettings = {
  // 开发模式：自动确认所有操作，延迟短
  development: () => {
    const controller = window.autoConfirmController;
    if (controller) {
      controller.enable();
      controller.setBehavior(true);
      controller.setDelay(200);
      controller.setNotifications(true);
      // 开发模式已启用：自动确认所有操作
    }
  },

  // 安全模式：只自动确认安全操作，危险操作需要手动确认
  safe: () => {
    const controller = window.autoConfirmController;
    if (controller) {
      controller.enable();
      controller.setBehavior(false); // 默认拒绝
      controller.setDelay(1000);
      controller.setNotifications(true);
      // 安全模式已启用：危险操作需要手动确认
    }
  },

  // 生产模式：禁用自动确认
  production: () => {
    const controller = window.autoConfirmController;
    if (controller) {
      controller.disable();
      // 生产模式已启用：禁用自动确认
    }
  },

  // 演示模式：自动确认，延迟长一些以便观察
  demo: () => {
    const controller = window.autoConfirmController;
    if (controller) {
      controller.enable();
      controller.setBehavior(true);
      controller.setDelay(1500);
      controller.setNotifications(true);
      // 演示模式已启用：自动确认，延迟1.5秒
    }
  }
};

// 挂载到全局对象方便调试
window.quickSettings = quickSettings;
window.smartConfirm = smartConfirm;
window.quickToggle = quickToggle;

// 确认替换器已加载
// 使用 quickSettings.development() 启用开发模式
// 使用 quickSettings.safe() 启用安全模式
// 使用 quickSettings.production() 启用生产模式
// 使用 quickSettings.demo() 启用演示模式
