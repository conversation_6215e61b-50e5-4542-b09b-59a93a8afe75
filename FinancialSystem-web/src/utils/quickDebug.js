/**
 * 快速调试工具
 * 用于检查自动确认系统是否正确加载
 * 仅在开发环境中使用
 */

// 在生产环境中禁用调试工具
if (process.env.NODE_ENV === 'development') {
  // 检查系统状态的函数
  window.checkAutoSystem = () => {
    // 检查控制器
    if (window.autoConfirmController) {
      // 控制器已加载
    } else {
      // 控制器未加载
    }

    // 检查命令界面
    if (window.autoCommand) {
      // 命令界面已加载
    } else {
      // 命令界面未加载
    }

    // 检查快速设置
    if (window.quickSettings) {
      // 快速设置已加载
    } else {
      // 快速设置未加载
    }

    // 检查其他全局函数
    const globalFunctions = [
      'enableAutoConfirm',
      'disableAutoConfirm',
      'toggleAutoConfirm',
      'setAutoConfirmBehavior',
      'smartConfirm',
      'quickToggle'
    ];

    return {
      controller: !!window.autoConfirmController,
      commandInterface: !!window.autoCommand,
      quickSettings: !!window.quickSettings,
      globalFunctions: globalFunctions.filter(func => !!window[func])
    };
  };

  // 强制重新加载自动确认系统
  window.reloadAutoSystem = () => {
    // 清除可能的缓存
    delete window.autoConfirmController;
    delete window.autoCommand;
    delete window.quickSettings;

    // 重新导入模块
    import('./autoConfirmController.js').then(() => {
      import('./commandInterface.js').then(() => {
        import('./confirmReplacer.js').then(() => {
          // 检查状态
          setTimeout(() => {
            window.checkAutoSystem();
          }, 500);
        });
      });
    });
  };

  // 手动启用开发模式
  window.forceDevMode = () => {
    if (window.autoConfirmController) {
      window.autoConfirmController.enable();
      window.autoConfirmController.setBehavior(true);
      window.autoConfirmController.setDelay(200);
      window.autoConfirmController.setNotifications(true);
    }
  };

  // 手动打开命令界面
  window.forceOpenCommand = () => {
    if (window.autoCommand) {
      window.autoCommand();
    } else {
      window.reloadAutoSystem();
    }
  };

  // 显示帮助信息
  window.autoHelp = () => {
    // 帮助信息已省略以减少控制台输出
  };

  // 延迟检查，确保其他模块已加载
  setTimeout(() => {
    window.checkAutoSystem();
  }, 1000);
}
