/**
 * 自动确认服务
 * 提供配置化的自动确认机制，可以替代手动的 confirm() 调用
 */

class AutoConfirmService {
  constructor() {
    this.config = {
      // 全局开关
      enabled: false,
      // 默认行为：true=自动确认，false=自动拒绝
      defaultBehavior: true,
      // 特定操作的配置
      operations: {
        // 删除操作
        delete: {
          enabled: false,
          autoConfirm: true,
          delay: 0 // 延迟时间（毫秒）
        },
        // 权限撤销
        revokePermission: {
          enabled: false,
          autoConfirm: true,
          delay: 500
        },
        // 批量操作
        batchOperation: {
          enabled: false,
          autoConfirm: false, // 批量操作默认需要确认
          delay: 1000
        },
        // 数据提交
        dataSubmission: {
          enabled: false,
          autoConfirm: true,
          delay: 300
        },
        // 过期权限
        expirePermissions: {
          enabled: false,
          autoConfirm: false,
          delay: 1500
        }
      },
      // 开发模式配置
      development: {
        logEnabled: true,
        showToast: true
      }
    };

    // 从 localStorage 加载配置
    this.loadConfig();
  }

  /**
   * 从 localStorage 加载配置
   */
  loadConfig() {
    try {
      const savedConfig = localStorage.getItem('autoConfirmConfig');
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig);
        this.config = { ...this.config, ...parsedConfig };
      }
    } catch (error) {
      console.warn('Failed to load auto-confirm config:', error);
    }
  }

  /**
   * 保存配置到 localStorage
   */
  saveConfig() {
    try {
      localStorage.setItem('autoConfirmConfig', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save auto-confirm config:', error);
    }
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新的配置对象
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
  }

  /**
   * 启用/禁用自动确认
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.config.enabled = enabled;
    this.saveConfig();
  }

  /**
   * 设置特定操作的自动确认
   * @param {string} operation - 操作类型
   * @param {boolean} enabled - 是否启用
   * @param {boolean} autoConfirm - 是否自动确认
   * @param {number} delay - 延迟时间
   */
  setOperationConfig(operation, enabled, autoConfirm = true, delay = 0) {
    if (!this.config.operations[operation]) {
      this.config.operations[operation] = {};
    }

    this.config.operations[operation] = {
      enabled,
      autoConfirm,
      delay
    };
    this.saveConfig();
  }

  /**
   * 自动确认函数，替代原生 confirm()
   * @param {string} message - 确认消息
   * @param {string} operationType - 操作类型
   * @param {Object} options - 额外选项
   * @returns {Promise<boolean>} - 确认结果
   */
  async autoConfirm(message, operationType = 'default', options = {}) {
    // 如果全局未启用，使用原生 confirm
    if (!this.config.enabled) {
      return window.confirm(message);
    }

    const operationConfig = this.config.operations[operationType];

    // 如果特定操作未配置或未启用，使用默认行为
    if (!operationConfig || !operationConfig.enabled) {
      if (this.config.development.logEnabled) {
        console.log(
          `Auto-confirm not configured for ${operationType}, using default behavior:`,
          this.config.defaultBehavior
        );
      }
      return this.config.defaultBehavior;
    }

    const { autoConfirm, delay } = operationConfig;

    // 记录日志
    if (this.config.development.logEnabled) {
      console.log(`Auto-confirm for ${operationType}:`, {
        message,
        autoConfirm,
        delay,
        operationType
      });
    }

    // 显示提示（如果启用）
    if (this.config.development.showToast) {
      this.showToast(`自动${autoConfirm ? '确认' : '取消'}: ${operationType}`, delay);
    }

    // 如果有延迟，等待指定时间
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    return autoConfirm;
  }

  /**
   * 显示提示消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时间
   */
  showToast(message, duration = 2000) {
    // 创建简单的 toast 提示
    const toast = document.createElement('div');
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #333;
      color: white;
      padding: 10px 15px;
      border-radius: 4px;
      z-index: 10000;
      font-size: 14px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      transition: opacity 0.3s ease;
    `;
    toast.textContent = message;
    document.body.appendChild(toast);

    // 自动移除
    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, duration);
  }

  /**
   * 创建增强的确认对话框（可替代原生 confirm）
   * @param {string} message - 确认消息
   * @param {string} operationType - 操作类型
   * @param {Object} options - 额外选项
   * @returns {Promise<boolean>} - 确认结果
   */
  async enhancedConfirm(message, operationType = 'default', options = {}) {
    const {
      title = '确认操作',
      confirmText = '确定',
      cancelText = '取消',
      type = 'warning' // warning, danger, info
    } = options;

    // 检查是否应该自动确认
    if (this.config.enabled) {
      const operationConfig = this.config.operations[operationType];
      if (operationConfig && operationConfig.enabled) {
        return this.autoConfirm(message, operationType, options);
      }
    }

    // 如果没有配置自动确认，显示增强的确认对话框
    return new Promise(resolve => {
      // 创建模态对话框
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10001;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      `;

      const dialog = document.createElement('div');
      dialog.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 24px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        animation: slideIn 0.2s ease-out;
      `;

      // 添加动画样式
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideIn {
          from { transform: translateY(-20px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }
      `;
      document.head.appendChild(style);

      const titleEl = document.createElement('h3');
      titleEl.style.cssText = `
        margin: 0 0 16px 0;
        color: ${type === 'danger' ? '#d32f2f' : type === 'warning' ? '#f57f17' : '#1976d2'};
        font-size: 18px;
        font-weight: 600;
      `;
      titleEl.textContent = title;

      const messageEl = document.createElement('p');
      messageEl.style.cssText = `
        margin: 0 0 24px 0;
        color: #333;
        line-height: 1.5;
        font-size: 14px;
      `;
      messageEl.textContent = message;

      const buttonContainer = document.createElement('div');
      buttonContainer.style.cssText = `
        display: flex;
        justify-content: flex-end;
        gap: 8px;
      `;

      const cancelButton = document.createElement('button');
      cancelButton.style.cssText = `
        padding: 8px 16px;
        border: 1px solid #ddd;
        background: white;
        color: #666;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s;
      `;
      cancelButton.textContent = cancelText;
      cancelButton.onmouseover = () => {
        cancelButton.style.background = '#f5f5f5';
      };
      cancelButton.onmouseout = () => {
        cancelButton.style.background = 'white';
      };

      const confirmButton = document.createElement('button');
      confirmButton.style.cssText = `
        padding: 8px 16px;
        border: none;
        background: ${type === 'danger' ? '#d32f2f' : type === 'warning' ? '#f57f17' : '#1976d2'};
        color: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s;
      `;
      confirmButton.textContent = confirmText;
      confirmButton.onmouseover = () => {
        confirmButton.style.filter = 'brightness(0.9)';
      };
      confirmButton.onmouseout = () => {
        confirmButton.style.filter = 'brightness(1)';
      };

      // 事件处理
      const cleanup = () => {
        document.body.removeChild(modal);
        document.head.removeChild(style);
      };

      cancelButton.onclick = () => {
        cleanup();
        resolve(false);
      };

      confirmButton.onclick = () => {
        cleanup();
        resolve(true);
      };

      // ESC 键关闭
      const handleKeyPress = e => {
        if (e.key === 'Escape') {
          cleanup();
          resolve(false);
          document.removeEventListener('keydown', handleKeyPress);
        }
      };
      document.addEventListener('keydown', handleKeyPress);

      // 点击背景关闭
      modal.onclick = e => {
        if (e.target === modal) {
          cleanup();
          resolve(false);
        }
      };

      // 组装对话框
      buttonContainer.appendChild(cancelButton);
      buttonContainer.appendChild(confirmButton);
      dialog.appendChild(titleEl);
      dialog.appendChild(messageEl);
      dialog.appendChild(buttonContainer);
      modal.appendChild(dialog);
      document.body.appendChild(modal);

      // 聚焦到确认按钮
      confirmButton.focus();
    });
  }

  /**
   * 获取当前配置
   * @returns {Object} 当前配置
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 重置配置为默认值
   */
  resetConfig() {
    localStorage.removeItem('autoConfirmConfig');
    this.loadConfig();
  }
}

// 创建单例实例
const autoConfirmService = new AutoConfirmService();

// 导出便捷函数
export const autoConfirm = autoConfirmService.autoConfirm.bind(autoConfirmService);
export const enhancedConfirm = autoConfirmService.enhancedConfirm.bind(autoConfirmService);
export const setAutoConfirmEnabled = autoConfirmService.setEnabled.bind(autoConfirmService);
export const setOperationConfig = autoConfirmService.setOperationConfig.bind(autoConfirmService);
export const updateAutoConfirmConfig = autoConfirmService.updateConfig.bind(autoConfirmService);
export const getAutoConfirmConfig = autoConfirmService.getConfig.bind(autoConfirmService);
export const resetAutoConfirmConfig = autoConfirmService.resetConfig.bind(autoConfirmService);

export default autoConfirmService;
