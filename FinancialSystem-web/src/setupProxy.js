const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function (app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: {
        '^/api': '/api' // 如果不需要路径重写，可以保持路径不变
      },
      onProxyReq: function (proxyReq, req, res) {
        // 将前端的认证令牌传递到后端
        if (req.headers.authorization) {
          proxyReq.setHeader('Authorization', req.headers.authorization);
        }
      },
      onError: function (err, req, res) {
        console.error('代理错误:', err);
        res.writeHead(500, {
          'Content-Type': 'text/plain'
        });
        res.end('代理到后端服务器时出错，请确保后端服务正在运行');
      }
    })
  );
};
