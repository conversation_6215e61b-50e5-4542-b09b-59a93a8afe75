/**
 =========================================================
 * Material Dashboard 2 React - v2.2.0
 =========================================================

 * Product Page: https://www.creative-tim.com/product/material-dashboard-react
 * Copyright 2023 Creative Tim (https://www.creative-tim.com)

 Coded by www.creative-tim.com

 =========================================================

 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
 */

/**
 All of the routes for the Material Dashboard 2 React are added here,
 You can add a new route, customize the routes and delete the routes here.

 Once you add a new route on this file it will be visible automatically on
 the Sidenav.

 For adding a new route you can follow the existing routes in the routes array.
 1. The `type` key with the `collapse` value is used for a route.
 2. The `type` key with the `title` value is used for a title inside the Sidenav.
 3. The `type` key with the `divider` value is used for a divider between Sidenav items.
 4. The `name` key is used for the name of the route on the Sidenav.
 5. The `key` key is used for the key of the route (It will help you with the key prop inside a loop).
 6. The `icon` key is used for the icon of the route on the Sidenav, you have to add a node.
 7. The `collapse` key is used for making a collapsible item on the Sidenav that has other routes
 inside (nested routes), you need to pass the nested routes inside an array as a value for the `collapse` key.
 8. The `route` key is used to store the route location which is used for the react router.
 9. The `href` key is used to store the external links location.
 10. The `title` key is only for the item with the type of `title` and its used for the title text on the Sidenav.
 10. The `component` key is used to store the component of its route.
 */
import React from 'react';

// Material Dashboard 2 React layouts
import SignIn from 'layouts/authentication/sign-in';
import SignUp from 'layouts/authentication/sign-up';
import AssetManagement from 'layouts/assetmanagement';
import OverdueStatistics from 'layouts/debtmanagement/pages/OverdueStatistics';

import Icon from '@mui/material/Icon';
import OverdueDebtAdd from './layouts/debtmanagement/pages/OverdueDebtAdd';
import OverdueReductionUpdate from './layouts/debtmanagement/pages/OverdueReductionUpdate';
import DebtSearch from './layouts/debtmanagement/pages/DebtSearch';
import LitigationConversion from './layouts/debtmanagement/pages/LitigationConversion';
import UserManagement from 'layouts/usermanagement';
import ManagementReport from 'layouts/managementreport';
import DataMonitor from 'layouts/datamonitor';
import DataExportCenter from 'layouts/dataexport/pages/DataExportCenter';
import DataPermissions from 'layouts/dataPermissions';
import OAWorkflowExtractor from 'layouts/oaworkflow';
import ConsistencyCheck from 'layouts/consistency-check';

const routes = [
  {
    type: 'collapse',
    name: '登录',
    key: 'sign-in',
    icon: <Icon fontSize="small">login</Icon>,
    route: '/authentication/sign-in',
    component: <SignIn />,
    protected: false
  },
  {
    type: 'collapse',
    name: '注册',
    key: 'sign-up',
    icon: <Icon fontSize="small">assignment</Icon>,
    route: '/authentication/sign-up',
    component: <SignUp />,
    protected: false
  },
  {
    type: 'collapse',
    name: '资产管理',
    key: 'asset-management',
    icon: <Icon fontSize="small">account_balance_wallet</Icon>,
    route: '/asset-management',
    component: <AssetManagement />,
    protected: true
  },
  {
    type: 'collapse',
    name: '债权管理',
    key: 'debt-management',
    icon: <Icon fontSize="small">credit_card</Icon>,
    route: '/debt-management',
    protected: true,
    collapse: [
      {
        type: 'collapse',
        name: '逾期债权列表',
        key: 'overdue-statistics',
        icon: <Icon fontSize="small">list</Icon>,
        route: '/debt-management/Overdue-statistics',
        component: <OverdueStatistics />
      },

      {
        type: 'collapse',
        name: '逾期债权新增录入',
        key: 'overdue-debt-add',
        icon: <Icon fontSize="small">add_circle</Icon>,
        route: '/debt-management/OverdueDebt-form',
        component: <OverdueDebtAdd />
      },
      {
        type: 'collapse',
        name: '逾期债权处置更新',
        key: 'overdue-debt-update',
        icon: <Icon fontSize="small">update</Icon>,
        route: '/debt-management/OverdueDebt-reduction',
        component: <OverdueReductionUpdate />
      },
      {
        type: 'collapse',
        name: '债权债务记录查询',
        key: 'debt-search',
        icon: <Icon fontSize="small">search</Icon>,
        route: '/debt-management/debt-search',
        component: <DebtSearch />
      },
      {
        type: 'collapse',
        name: '诉讼和非诉讼互转',
        key: 'litigation-conversion',
        icon: <Icon fontSize="small">swap_horiz</Icon>,
        route: '/debt-management/litigation-conversion',
        component: <LitigationConversion />
      }
    ]
  },
  {
    type: 'collapse',
    name: '用户管理',
    key: 'user-management',
    icon: <Icon fontSize="small">people</Icon>,
    route: '/user-management',
    component: <UserManagement />,
    protected: true,
    adminOnly: true
  },
  {
    type: 'collapse',
    name: '数据权限管理',
    key: 'data-permissions',
    icon: <Icon fontSize="small">security</Icon>,
    route: '/data-permissions',
    component: <DataPermissions />,
    protected: true,
    adminOnly: true
  },
  {
    type: 'collapse',
    name: '管理报表',
    key: 'management-report',
    icon: <Icon fontSize="small">assessment</Icon>,
    route: '/management-report',
    component: <ManagementReport />,
    protected: true
  },
  {
    type: 'collapse',
    name: '数据导出中心',
    key: 'data-export-center',
    icon: <Icon fontSize="small">download</Icon>,
    route: '/data-export/center',
    component: <DataExportCenter />,
    protected: true
  },
  {
    type: 'collapse',
    name: '数据更新观测平台',
    key: 'data-monitor',
    icon: <Icon fontSize="small">dashboard</Icon>,
    route: '/data-monitor',
    component: <DataMonitor />,
    protected: true,
    adminOnly: true
  },
  {
    type: 'collapse',
    name: 'OA工作流提取',
    key: 'oa-workflow',
    icon: <Icon fontSize="small">workflow</Icon>,
    route: '/oa-workflow',
    component: <OAWorkflowExtractor />,
    protected: true
  },
  {
    type: 'collapse',
    name: '数据一致性检查',
    key: 'consistency-check',
    icon: <Icon fontSize="small">fact_check</Icon>,
    route: '/consistency-check',
    component: <ConsistencyCheck />,
    protected: true
  }
];

export default routes;
