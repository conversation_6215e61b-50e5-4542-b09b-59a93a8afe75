import { useState, useEffect } from 'react';
import {
  Tab,
  Tabs,
  Box,
  Typography,
  Alert,
  CircularProgress,
  Container,
  Paper,
  Divider
} from '@mui/material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import { useAuth } from 'context/AuthContext';

// 导入子组件
import UserPermissionsList from './components/UserPermissionsList';
import CompanyUsersList from './components/CompanyUsersList';
import PermissionGrantForm from './components/PermissionGrantForm';
import ExpiringPermissions from './components/ExpiringPermissions';

/**
 * 数据权限管理页面
 */
function DataPermissions() {
  const { user } = useAuth();
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isManagementAdmin, setIsManagementAdmin] = useState(false);

  // 检查用户是否为管理员
  useEffect(() => {
    const checkAdminStatus = () => {
      if (user?.role === 'ROLE_ADMIN') {
        setIsManagementAdmin(true);
      } else {
        setIsManagementAdmin(false);
      }
    };

    if (user) {
      checkAdminStatus();
    }
  }, [user]);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  // 如果不是管理员，显示权限不足
  if (!isManagementAdmin) {
    return (
      <DashboardLayout>
        <DashboardNavbar />
        <Container maxWidth="lg">
          <Box sx={{ mt: 6, mb: 3 }}>
            <Alert severity="error" sx={{ mb: 2 }}>
              您没有访问数据权限管理页面的权限。只有系统管理员才能管理数据访问权限。
            </Alert>
          </Box>
        </Container>
      </DashboardLayout>
    );
  }

  const styles = {
    paper: {
      padding: 2,
      margin: '10px 0',
      borderRadius: 1,
      boxShadow: '0 2px 8px 0 rgba(0,0,0,0.1)',
      backgroundColor: '#ffffff'
    },
    title: {
      fontSize: '18px',
      fontWeight: 600,
      color: '#1a237e',
      marginBottom: 1.5,
      textAlign: 'center'
    }
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="lg">
        <Box sx={{ mt: 2, mb: 3 }}>
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h5" sx={styles.title}>
              数据权限管理中心
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mb: 2 }}>
              管理用户对公司数据的访问权限，确保数据安全
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
                {error}
              </Alert>
            )}

            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
              <Tabs value={currentTab} onChange={handleTabChange}>
                <Tab label="用户权限列表" />
                <Tab label="权限授权管理" />
                <Tab label="公司用户管理" />
                <Tab label="即将过期权限" />
              </Tabs>
            </Box>

            {loading && (
              <Box display="flex" justifyContent="center" my={3}>
                <CircularProgress />
              </Box>
            )}

            {/* Tab 内容 */}
            {!loading && (
              <>
                {currentTab === 0 && <UserPermissionsList onError={setError} />}

                {currentTab === 1 && (
                  <PermissionGrantForm
                    onError={setError}
                    onSuccess={() => setCurrentTab(0)} // 成功后跳转到用户权限列表
                  />
                )}

                {currentTab === 2 && <CompanyUsersList onError={setError} />}

                {currentTab === 3 && <ExpiringPermissions onError={setError} />}
              </>
            )}
          </Paper>
        </Box>
      </Container>
    </DashboardLayout>
  );
}

export default DataPermissions;
