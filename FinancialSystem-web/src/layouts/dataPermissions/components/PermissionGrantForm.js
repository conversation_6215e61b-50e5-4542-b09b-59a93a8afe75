import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Chip
} from '@mui/material';
import { ExpandMore as ExpandMoreIcon } from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import zhCN from 'date-fns/locale/zh-CN';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';
import api from 'utils/api';

/**
 * 权限授权表单组件
 */
function PermissionGrantForm({ onError, onSuccess }) {
  const [users, setUsers] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    userId: '',
    companyId: '',
    permissionType: 'read',
    expiresAt: null,
    remarks: ''
  });
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [userPermissions, setUserPermissions] = useState([]);

  useEffect(() => {
    fetchUsers();
    fetchCompanies();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await api.get('/users');
      setUsers(response.data || []);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      onError && onError('获取用户列表失败：' + (error.response?.data?.message || error.message));
    }
  };

  const fetchCompanies = async () => {
    try {
      const response = await api.get('/companies');

      // 检查不同的响应格式
      if (response.data) {
        if (Array.isArray(response.data)) {
          // 如果直接返回数组
          setCompanies(response.data);
        } else if (response.data.data && Array.isArray(response.data.data)) {
          // 如果数据在 data.data 中
          setCompanies(response.data.data);
        } else if (response.data.companies && Array.isArray(response.data.companies)) {
          // 如果数据在 data.companies 中
          setCompanies(response.data.companies);
        } else {
          setCompanies([]);
        }
      } else {
        setCompanies([]);
      }
    } catch (error) {
      console.error('获取公司列表失败:', error);
      onError && onError('获取公司列表失败：' + (error.response?.data?.message || error.message));
    }
  };

  const fetchUserPermissions = async userId => {
    try {
      const response = await api.get(`/data-permissions/user-permissions?userId=${userId}`);
      setUserPermissions(response.data?.data || []);
    } catch (error) {
      console.error('获取用户权限失败:', error);
    }
  };

  const handleUserChange = async userId => {
    const user = users.find(u => u.id === userId);
    setSelectedUser(user);
    setFormData({ ...formData, userId });

    if (userId) {
      await fetchUserPermissions(userId);
    } else {
      setUserPermissions([]);
    }
  };

  const handleCompanyChange = companyId => {
    const company = companies.find(c => c.id === companyId);
    setSelectedCompany(company);
    setFormData({ ...formData, companyId });
  };

  const handleSubmit = async e => {
    e.preventDefault();

    if (!formData.userId || !formData.companyId) {
      onError && onError('请选择用户和公司');
      return;
    }

    setLoading(true);
    try {
      const requestData = {
        ...formData,
        expiresAt: formData.expiresAt ? formData.expiresAt.toISOString() : null
      };

      await api.post('/data-permissions/grant', requestData);

      // 重置表单
      setFormData({
        userId: '',
        companyId: '',
        permissionType: 'read',
        expiresAt: null,
        remarks: ''
      });
      setSelectedUser(null);
      setSelectedCompany(null);
      setUserPermissions([]);

      alert('权限授权成功');
      onSuccess && onSuccess();
    } catch (error) {
      console.error('权限授权失败:', error);
      onError && onError('权限授权失败：' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const getPermissionTypeLabel = type => {
    const typeMap = {
      read: '只读权限',
      write: '读写权限',
      admin: '管理员权限'
    };
    return typeMap[type] || type;
  };

  const getPermissionTypeColor = type => {
    const colorMap = {
      read: 'info',
      write: 'success',
      admin: 'error'
    };
    return colorMap[type] || 'default';
  };

  // 检查用户是否已有该公司权限
  const hasCompanyPermission = companyId => {
    return userPermissions.some(
      p =>
        p.company?.id === companyId &&
        p.status === 'active' &&
        (p.expiresAt === null || new Date(p.expiresAt) > new Date())
    );
  };

  return (
    <MDBox>
      <Card>
        <CardContent>
          <MDTypography variant="h6" gutterBottom>
            授权用户公司数据访问权限
          </MDTypography>

          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              {/* 用户选择 */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>选择用户</InputLabel>
                  <Select
                    value={formData.userId}
                    onChange={e => handleUserChange(e.target.value)}
                    label="选择用户"
                  >
                    <MenuItem value="">请选择用户</MenuItem>
                    {users.map(user => (
                      <MenuItem key={user.id} value={user.id}>
                        {user.name} ({user.username}) - {user.company}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* 公司选择 */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>选择公司</InputLabel>
                  <Select
                    value={formData.companyId}
                    onChange={e => handleCompanyChange(e.target.value)}
                    label="选择公司"
                    disabled={!formData.userId}
                  >
                    <MenuItem value="">请选择公司</MenuItem>
                    {companies.map(company => (
                      <MenuItem
                        key={company.id}
                        value={company.id}
                        disabled={hasCompanyPermission(company.id)}
                      >
                        {company.companyName}
                        {company.isManagementCompany && ' (管理公司)'}
                        {hasCompanyPermission(company.id) && ' (已有权限)'}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* 权限类型 */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>权限类型</InputLabel>
                  <Select
                    value={formData.permissionType}
                    onChange={e => setFormData({ ...formData, permissionType: e.target.value })}
                    label="权限类型"
                  >
                    <MenuItem value="read">只读权限</MenuItem>
                    <MenuItem value="write">读写权限</MenuItem>
                    <MenuItem value="admin">管理员权限</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* 过期时间 */}
              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
                  <DateTimePicker
                    label="过期时间 (可选)"
                    value={formData.expiresAt}
                    onChange={value => setFormData({ ...formData, expiresAt: value })}
                    renderInput={params => <TextField {...params} fullWidth />}
                    minDateTime={new Date()}
                  />
                </LocalizationProvider>
              </Grid>

              {/* 备注 */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="授权备注"
                  multiline
                  rows={3}
                  value={formData.remarks}
                  onChange={e => setFormData({ ...formData, remarks: e.target.value })}
                  placeholder="请输入授权原因或备注信息"
                />
              </Grid>

              {/* 提交按钮 */}
              <Grid item xs={12}>
                <MDBox display="flex" gap={2}>
                  <MDButton
                    variant="contained"
                    color="info"
                    type="submit"
                    disabled={loading || !formData.userId || !formData.companyId}
                  >
                    {loading ? '授权中...' : '确认授权'}
                  </MDButton>

                  <MDButton
                    variant="outlined"
                    color="secondary"
                    onClick={() => {
                      setFormData({
                        userId: '',
                        companyId: '',
                        permissionType: 'read',
                        expiresAt: null,
                        remarks: ''
                      });
                      setSelectedUser(null);
                      setSelectedCompany(null);
                      setUserPermissions([]);
                    }}
                  >
                    重置表单
                  </MDButton>
                </MDBox>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>

      {/* 用户权限预览 */}
      {selectedUser && userPermissions.length > 0 && (
        <Accordion sx={{ mt: 3 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>当前用户权限预览 ({userPermissions.length} 个权限)</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              {userPermissions.map(permission => (
                <Grid item xs={12} sm={6} md={4} key={permission.id}>
                  <Card variant="outlined" sx={{ p: 2 }}>
                    <MDTypography variant="subtitle2" gutterBottom>
                      {permission.company?.companyName}
                    </MDTypography>
                    <MDBox display="flex" gap={1} mb={1}>
                      <Chip
                        label={getPermissionTypeLabel(permission.permissionType)}
                        color={getPermissionTypeColor(permission.permissionType)}
                        size="small"
                      />
                      {permission.isDefault && <Chip label="默认" color="default" size="small" />}
                    </MDBox>
                    <MDTypography variant="caption" color="text.secondary">
                      {permission.expiresAt
                        ? `过期时间: ${new Date(permission.expiresAt).toLocaleDateString()}`
                        : '永久有效'}
                    </MDTypography>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </AccordionDetails>
        </Accordion>
      )}

      {/* 权限说明 */}
      <Alert severity="info" sx={{ mt: 3 }}>
        <strong>权限说明：</strong>
        <br />• <strong>只读权限</strong>：只能查看数据，不能修改
        <br />• <strong>读写权限</strong>：可以查看和修改数据
        <br />• <strong>管理员权限</strong>：具有完全的数据管理权限
        <br />
        • 万润科技的管理员默认拥有所有公司的管理员权限
        <br />• 用户对自己所属公司默认拥有读写权限
      </Alert>
    </MDBox>
  );
}

PermissionGrantForm.propTypes = {
  onError: PropTypes.func,
  onSuccess: PropTypes.func
};

export default PermissionGrantForm;
