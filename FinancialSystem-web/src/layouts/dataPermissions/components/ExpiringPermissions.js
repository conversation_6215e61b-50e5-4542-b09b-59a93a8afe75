import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Chip, Alert, Card, CardContent, Tooltip } from '@mui/material';
import {
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  AccessTime as AccessTimeIcon
} from '@mui/icons-material';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';
import GenericDataTable from 'components/tables/GenericDataTable';
import api from 'utils/api';

/**
 * 即将过期权限组件
 */
function ExpiringPermissions({ onError }) {
  const [expiringPermissions, setExpiringPermissions] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchExpiringPermissions();
  }, []);

  const fetchExpiringPermissions = async () => {
    setLoading(true);
    try {
      const response = await api.get('/data-permissions/expiring');
      setExpiringPermissions(response.data?.data || []);
    } catch (error) {
      console.error('获取即将过期权限失败:', error);
      onError &&
        onError('获取即将过期权限失败：' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleExpirePermissions = async () => {
    if (!window.confirm('确定要手动过期所有已过期的权限吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await api.post('/data-permissions/expire');
      const expiredCount = response.data?.expiredCount || 0;

      alert(`成功过期 ${expiredCount} 个权限记录`);

      // 刷新列表
      await fetchExpiringPermissions();
    } catch (error) {
      console.error('手动过期权限失败:', error);
      onError && onError('手动过期权限失败：' + (error.response?.data?.message || error.message));
    }
  };

  const getPermissionTypeLabel = type => {
    const typeMap = {
      read: '只读',
      write: '读写',
      admin: '管理员'
    };
    return typeMap[type] || type;
  };

  const getPermissionTypeColor = type => {
    const colorMap = {
      read: 'info',
      write: 'success',
      admin: 'error'
    };
    return colorMap[type] || 'default';
  };

  const getDaysUntilExpiry = expiresAt => {
    if (!expiresAt) {
      return null;
    }

    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffTime = expiry - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  const getExpiryChip = expiresAt => {
    const days = getDaysUntilExpiry(expiresAt);

    if (days === null) {
      return null;
    }

    if (days < 0) {
      return (
        <Chip
          label={`已过期 ${Math.abs(days)} 天`}
          color="error"
          size="small"
          icon={<WarningIcon />}
        />
      );
    } else if (days === 0) {
      return <Chip label="今天过期" color="error" size="small" icon={<AccessTimeIcon />} />;
    } else if (days <= 3) {
      return (
        <Chip label={`${days} 天后过期`} color="error" size="small" icon={<AccessTimeIcon />} />
      );
    } else if (days <= 7) {
      return (
        <Chip label={`${days} 天后过期`} color="warning" size="small" icon={<AccessTimeIcon />} />
      );
    } else {
      return (
        <Chip label={`${days} 天后过期`} color="info" size="small" icon={<AccessTimeIcon />} />
      );
    }
  };

  // 按过期紧急程度排序
  const sortedPermissions = [...expiringPermissions].sort((a, b) => {
    const daysA = getDaysUntilExpiry(a.expiresAt);
    const daysB = getDaysUntilExpiry(b.expiresAt);

    if (daysA === null && daysB === null) {
      return 0;
    }
    if (daysA === null) {
      return 1;
    }
    if (daysB === null) {
      return -1;
    }

    return daysA - daysB;
  });

  // 统计信息
  const expiredCount = sortedPermissions.filter(p => getDaysUntilExpiry(p.expiresAt) < 0).length;
  const todayCount = sortedPermissions.filter(p => getDaysUntilExpiry(p.expiresAt) === 0).length;
  const within3DaysCount = sortedPermissions.filter(p => {
    const days = getDaysUntilExpiry(p.expiresAt);
    return days > 0 && days <= 3;
  }).length;
  const within7DaysCount = sortedPermissions.filter(p => {
    const days = getDaysUntilExpiry(p.expiresAt);
    return days > 3 && days <= 7;
  }).length;

  return (
    <MDBox>
      {/* 操作按钮 */}
      <MDBox display="flex" gap={2} mb={3} alignItems="center">
        <MDButton
          variant="outlined"
          color="info"
          startIcon={<RefreshIcon />}
          onClick={fetchExpiringPermissions}
          disabled={loading}
        >
          {loading ? '刷新中...' : '刷新列表'}
        </MDButton>

        {expiredCount > 0 && (
          <MDButton variant="contained" color="error" onClick={handleExpirePermissions}>
            清理已过期权限 ({expiredCount})
          </MDButton>
        )}
      </MDBox>

      {/* 统计信息 */}
      {sortedPermissions.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <MDTypography variant="h6" gutterBottom>
              权限过期统计
            </MDTypography>
            <MDBox display="flex" gap={2} flexWrap="wrap">
              {expiredCount > 0 && (
                <Chip label={`已过期: ${expiredCount}`} color="error" icon={<WarningIcon />} />
              )}
              {todayCount > 0 && (
                <Chip label={`今天过期: ${todayCount}`} color="error" icon={<AccessTimeIcon />} />
              )}
              {within3DaysCount > 0 && (
                <Chip
                  label={`3天内过期: ${within3DaysCount}`}
                  color="error"
                  icon={<AccessTimeIcon />}
                />
              )}
              {within7DaysCount > 0 && (
                <Chip
                  label={`7天内过期: ${within7DaysCount}`}
                  color="warning"
                  icon={<AccessTimeIcon />}
                />
              )}
            </MDBox>
          </CardContent>
        </Card>
      )}

      {/* 权限列表 */}
      {loading ? (
        <MDBox textAlign="center" py={3}>
          <MDTypography variant="body2">加载中...</MDTypography>
        </MDBox>
      ) : sortedPermissions.length === 0 ? (
        <Alert severity="success">
          <strong>太好了！</strong> 当前没有即将过期的权限。
        </Alert>
      ) : (
        <Card>
          <CardContent>
            <MDTypography variant="h6" gutterBottom>
              即将过期权限列表 ({sortedPermissions.length} 个)
            </MDTypography>

            <GenericDataTable
              columns={[
                { field: 'user', headerName: '用户', width: '15%' },
                { field: 'company', headerName: '公司', width: '18%' },
                { field: 'permissionType', headerName: '权限类型', width: '10%' },
                { field: 'expiryStatus', headerName: '过期状态', width: '13%' },
                { field: 'expiresAt', headerName: '过期时间', width: '16%' },
                { field: 'grantedAt', headerName: '授权时间', width: '10%' },
                { field: 'grantedBy', headerName: '授权人', width: '10%' },
                { field: 'remarks', headerName: '备注', width: '8%' }
              ]}
              data={sortedPermissions.map(permission => ({
                ...permission,
                user: (
                  <MDBox>
                    <MDTypography variant="subtitle2" sx={{ fontSize: '20px' }}>
                      {permission.user?.name}
                    </MDTypography>
                    <MDTypography
                      variant="caption"
                      color="text.secondary"
                      sx={{ fontSize: '16px' }}
                    >
                      {permission.user?.username}
                    </MDTypography>
                  </MDBox>
                ),
                company: (
                  <MDBox>
                    <MDTypography variant="body2" sx={{ fontSize: '20px' }}>
                      {permission.company?.companyName}
                    </MDTypography>
                    <MDTypography
                      variant="caption"
                      color="text.secondary"
                      sx={{ fontSize: '16px' }}
                    >
                      用户所属: {permission.user?.companyname}
                    </MDTypography>
                  </MDBox>
                ),
                permissionType: (
                  <Chip
                    label={getPermissionTypeLabel(permission.permissionType)}
                    color={getPermissionTypeColor(permission.permissionType)}
                    size="small"
                  />
                ),
                expiryStatus: getExpiryChip(permission.expiresAt),
                expiresAt: new Date(permission.expiresAt).toLocaleString(),
                grantedAt: permission.grantedAt
                  ? new Date(permission.grantedAt).toLocaleDateString()
                  : '-',
                grantedBy: permission.grantedBy?.username || '-',
                remarks: (
                  <Tooltip title={permission.remarks || '无备注'}>
                    <MDTypography
                      variant="caption"
                      sx={{
                        maxWidth: 100,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        display: 'block',
                        fontSize: '16px'
                      }}
                    >
                      {permission.remarks || '-'}
                    </MDTypography>
                  </Tooltip>
                )
              }))}
              pageSize={10}
              fontSize={20}
              rowHeight={55}
            />
          </CardContent>
        </Card>
      )}

      {/* 说明信息 */}
      <Alert severity="info" sx={{ mt: 3 }}>
        <strong>说明：</strong>
        <br />
        • 系统会自动检查7天内即将过期的权限
        <br />
        • 已过期的权限会自动失效，用户将无法访问相关数据
        <br />
        • 建议及时续期重要的权限，或者联系相关用户重新申请权限
        <br />• 红色背景表示已过期或即将过期的紧急权限
      </Alert>
    </MDBox>
  );
}

ExpiringPermissions.propTypes = {
  onError: PropTypes.func
};

export default ExpiringPermissions;
