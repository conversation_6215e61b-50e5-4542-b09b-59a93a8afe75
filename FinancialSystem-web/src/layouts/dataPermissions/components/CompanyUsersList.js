import { useState, useEffect } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Card,
  CardContent,
  Alert,
  IconButton
} from '@mui/material';
import { Delete as DeleteIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import PropTypes from 'prop-types';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';
import GenericDataTable from 'components/tables/GenericDataTable';
import api from 'utils/api';

/**
 * 公司用户权限列表组件
 */
function CompanyUsersList({ onError }) {
  const [companies, setCompanies] = useState([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState('');
  const [companyUsers, setCompanyUsers] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchCompanies();
  }, []);

  useEffect(() => {
    if (selectedCompanyId) {
      fetchCompanyUsers();
    } else {
      setCompanyUsers([]);
    }
  }, [selectedCompanyId]);

  const fetchCompanies = async () => {
    try {
      const response = await api.get('/companies');
      setCompanies(response.data?.data || []);
    } catch (error) {
      console.error('获取公司列表失败:', error);
      onError && onError('获取公司列表失败：' + (error.response?.data?.message || error.message));
    }
  };

  const fetchCompanyUsers = async () => {
    if (!selectedCompanyId) {
      return;
    }

    setLoading(true);
    try {
      const response = await api.get(
        `/data-permissions/company-users?companyId=${selectedCompanyId}`
      );
      setCompanyUsers(response.data?.data || []);
    } catch (error) {
      console.error('获取公司用户权限列表失败:', error);
      onError &&
        onError('获取公司用户权限列表失败：' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleRevokePermission = async (userId, companyId) => {
    if (!window.confirm('确定要撤销该用户的权限吗？')) {
      return;
    }

    try {
      await api.post('/data-permissions/revoke', {
        userId: userId,
        companyId: companyId
      });

      // 刷新列表
      await fetchCompanyUsers();

      alert('权限撤销成功');
    } catch (error) {
      console.error('撤销权限失败:', error);
      onError && onError('撤销权限失败：' + (error.response?.data?.message || error.message));
    }
  };

  const getPermissionTypeLabel = type => {
    const typeMap = {
      read: '只读',
      write: '读写',
      admin: '管理员'
    };
    return typeMap[type] || type;
  };

  const getPermissionTypeColor = type => {
    const colorMap = {
      read: 'info',
      write: 'success',
      admin: 'error'
    };
    return colorMap[type] || 'default';
  };

  const getStatusLabel = status => {
    const statusMap = {
      active: '激活',
      suspended: '暂停',
      expired: '过期'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = status => {
    const colorMap = {
      active: 'success',
      suspended: 'warning',
      expired: 'error'
    };
    return colorMap[status] || 'default';
  };

  const selectedCompany = companies.find(c => c.id.toString() === selectedCompanyId);

  return (
    <MDBox>
      {/* 公司选择 */}
      <MDBox mb={3}>
        <FormControl fullWidth sx={{ maxWidth: 400 }}>
          <InputLabel>选择公司</InputLabel>
          <Select
            value={selectedCompanyId}
            onChange={e => setSelectedCompanyId(e.target.value)}
            label="选择公司"
          >
            <MenuItem value="">请选择公司</MenuItem>
            {companies.map(company => (
              <MenuItem key={company.id} value={company.id.toString()}>
                {company.companyName}
                {company.isManagementCompany && ' (管理公司)'}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {selectedCompanyId && (
          <MDButton
            variant="outlined"
            color="info"
            startIcon={<RefreshIcon />}
            onClick={fetchCompanyUsers}
            sx={{ ml: 2 }}
          >
            刷新列表
          </MDButton>
        )}
      </MDBox>

      {/* 公司信息 */}
      {selectedCompany && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <MDTypography variant="h6" gutterBottom>
              {selectedCompany.companyName}
              {selectedCompany.isManagementCompany && ' (管理公司)'}
            </MDTypography>
            <MDTypography variant="body2" color="text.secondary">
              公司代码：{selectedCompany.companyCode || '暂无'}
            </MDTypography>
            <MDTypography variant="body2" color="text.secondary">
              状态：{selectedCompany.status === 'active' ? '活跃' : '非活跃'}
            </MDTypography>
            <MDTypography variant="body2" color="text.secondary">
              有权限用户数：{companyUsers.length} 人
            </MDTypography>
          </CardContent>
        </Card>
      )}

      {/* 用户权限列表 */}
      {selectedCompanyId && (
        <>
          {loading ? (
            <MDBox textAlign="center" py={3}>
              <MDTypography variant="body2">加载中...</MDTypography>
            </MDBox>
          ) : companyUsers.length === 0 ? (
            <Alert severity="info">该公司暂无用户拥有数据访问权限</Alert>
          ) : (
            <Card>
              <CardContent>
                <MDTypography variant="h6" gutterBottom>
                  用户权限列表 ({companyUsers.length} 人)
                </MDTypography>

                <GenericDataTable
                  columns={[
                    { field: 'user', headerName: '用户', width: '12%' },
                    { field: 'companyname', headerName: '所属公司', width: '12%' },
                    { field: 'department', headerName: '部门', width: '10%' },
                    { field: 'permissionType', headerName: '权限类型', width: '8%' },
                    { field: 'isDefault', headerName: '默认', width: '6%' },
                    { field: 'status', headerName: '状态', width: '8%' },
                    { field: 'expiresAt', headerName: '过期时间', width: '10%' },
                    { field: 'grantedAt', headerName: '授权时间', width: '10%' },
                    { field: 'grantedBy', headerName: '授权人', width: '8%' },
                    { field: 'remarks', headerName: '备注', width: '10%' }
                  ]}
                  data={companyUsers.map(permission => ({
                    ...permission,
                    user: (
                      <MDBox>
                        <MDTypography variant="subtitle2" sx={{ fontSize: '20px' }}>
                          {permission.user?.name}
                        </MDTypography>
                        <MDTypography
                          variant="caption"
                          color="text.secondary"
                          sx={{ fontSize: '16px' }}
                        >
                          {permission.user?.username}
                        </MDTypography>
                      </MDBox>
                    ),
                    companyname: permission.user?.companyname || '-',
                    department: permission.user?.department || '-',
                    permissionType: (
                      <Chip
                        label={getPermissionTypeLabel(permission.permissionType)}
                        color={getPermissionTypeColor(permission.permissionType)}
                        size="small"
                      />
                    ),
                    isDefault: (
                      <Chip
                        label={permission.isDefault ? '是' : '否'}
                        color={permission.isDefault ? 'success' : 'default'}
                        size="small"
                      />
                    ),
                    status: (
                      <Chip
                        label={getStatusLabel(permission.status)}
                        color={getStatusColor(permission.status)}
                        size="small"
                      />
                    ),
                    expiresAt: permission.expiresAt
                      ? new Date(permission.expiresAt).toLocaleDateString()
                      : '永久',
                    grantedAt: permission.grantedAt
                      ? new Date(permission.grantedAt).toLocaleDateString()
                      : '-',
                    grantedBy: permission.grantedBy?.username || '-',
                    remarks: (
                      <MDTypography
                        variant="caption"
                        sx={{
                          maxWidth: 120,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          display: 'block',
                          fontSize: '16px'
                        }}
                        title={permission.remarks}
                      >
                        {permission.remarks || '-'}
                      </MDTypography>
                    )
                  }))}
                  pageSize={10}
                  fontSize={20}
                  renderActions={permission =>
                    !permission.isDefault && permission.status === 'active' ? (
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() =>
                          handleRevokePermission(permission.user?.id, permission.company?.id)
                        }
                        title="撤销权限"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    ) : null
                  }
                  actionColumnWidth="6%"
                  actionColumnTitle="操作"
                />
              </CardContent>
            </Card>
          )}
        </>
      )}

      {!selectedCompanyId && <Alert severity="info">请选择一个公司查看其用户权限列表</Alert>}
    </MDBox>
  );
}

CompanyUsersList.propTypes = {
  onError: PropTypes.func
};

export default CompanyUsersList;
