import api from '../../../utils/api';

export const fetchDebtStatistics = async (year, month, company) => {
  try {
    // 检查认证令牌是否存在
    const token = localStorage.getItem('token');
    if (!token) {
    } else {
    }

    const response = await api.get('/debts/statistics', {
      params: {
        year,
        month,
        company
      }
    });

    return response.data;
  } catch (error) {
    console.error('获取债权统计数据失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

export const fetchDebtStatisticsDetail = async (year, month, company) => {
  try {
    // 检查认证令牌是否存在
    const token = localStorage.getItem('token');
    if (!token) {
    }

    const response = await api.get('/debts/statistics/detail', {
      params: {
        year,
        month,
        company
      }
    });

    console.log(
      '债权统计详情获取成功:',
      '新增债权条目数:',
      (response.data.newDebtDetailList || []).length,
      '减少债权条目数:',
      (response.data.reductionDebtDetailList || []).length
    );
    return response.data;
  } catch (error) {
    console.error('获取债权统计详情失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

// 获取存量债权清收情况
export const fetchDebtCollectionStatus = async (year, month, company) => {
  try {
    // 检查认证令牌是否存在
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('未找到认证令牌');
    }

    const response = await api.get('/debts/statistics/collection-status', {
      params: {
        year,
        month,
        company
      }
    });

    console.log('存量债权清收情况获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取存量债权清收情况失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

// 获取新增债权清收情况
export const fetchNewDebtCollectionStatus = async (year, month, company) => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('未找到认证令牌');
    }

    const response = await api.get('/debts/statistics/new-debt-trend', {
      params: {
        year,
        month,
        company
      }
    });

    console.log('新增债权清收情况获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取新增债权清收情况失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

// 获取新增债权处置方式统计
export const fetchNewDebtDisposalMethods = async (year, month, company) => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('未找到认证令牌');
    }

    const response = await api.get('/debts/statistics/new-debt-balance-by-company', {
      params: {
        year,
        month,
        company
      }
    });

    console.log('新增债权处置方式统计获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取新增债权处置方式统计失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

// 获取各子公司新增债权回收进度
export const fetchNewDebtCompanyProgress = async (year, month) => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('未找到认证令牌');
    }

    const response = await api.get('/debts/statistics/new-debt-recovery-comparison', {
      params: {
        year,
        month
      }
    });

    console.log('各子公司新增债权回收进度获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取各子公司新增债权回收进度失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

// 获取处置方式统计
export const fetchDebtDisposalMethods = async (year, month, company) => {
  try {
    // 检查认证令牌是否存在
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('未找到认证令牌');
    }

    const response = await api.get('/debts/statistics/disposal-methods', {
      params: {
        year,
        month,
        company
      }
    });

    console.log('处置方式统计获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取处置方式统计失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

// 获取公司回收进度
export const fetchCompanyRecoveryProgress = async (year, month) => {
  try {
    // 检查认证令牌是否存在
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('未找到认证令牌');
    }

    const response = await api.get('/debts/statistics/company-progress', {
      params: {
        year,
        month
      }
    });

    console.log('公司回收进度获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取公司回收进度失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};
