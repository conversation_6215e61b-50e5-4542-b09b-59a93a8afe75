/**
 * OverdueStatistics 集成测试
 *
 * 验证新增功能的集成：
 * 1. 新的API调用函数是否正确导入
 * 2. 新的图表组件是否正确导入
 * 3. 状态管理是否正确设置
 * 4. getDebtStatisticsData 函数是否调用了新的API
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import OverdueStatistics from '../OverdueStatistics';

// Mock dependencies
jest.mock('examples/LayoutContainers/DashboardLayout', () => ({ children }) => (
  <div>{children}</div>
));
jest.mock('examples/Navbars/DashboardNavbar', () => () => <div>Navbar</div>);
jest.mock('../components/OverdueStatisticsFilter', () => () => <div>Filter</div>);
jest.mock('../components/DebtStatisticsChart', () => () => <div>DebtStatisticsChart</div>);
jest.mock('../components/DownloadExcelButton', () => () => <button>Download</button>);
jest.mock('../components/CustomPieChart', () => () => <div>CustomPieChart</div>);
jest.mock('../components/BarStatisticsChart', () => () => <div>BarStatisticsChart</div>);
jest.mock('components/tables/GenericDataTable', () => () => <div>GenericDataTable</div>);

// Mock new chart components
jest.mock('../components/CollectionStatusChart', () => ({ data, title }) => (
  <div data-testid="collection-status-chart">CollectionStatusChart - {title}</div>
));
jest.mock('../components/DisposalMethodsChart', () => ({ data, title }) => (
  <div data-testid="disposal-methods-chart">DisposalMethodsChart - {title}</div>
));
jest.mock('../components/CompanyProgressChart', () => ({ data, title }) => (
  <div data-testid="company-progress-chart">CompanyProgressChart - {title}</div>
));

// Mock API functions
const mockFetchDebtStatistics = jest.fn();
const mockFetchDebtStatisticsDetail = jest.fn();
const mockFetchDebtCollectionStatus = jest.fn();
const mockFetchDebtDisposalMethods = jest.fn();
const mockFetchCompanyRecoveryProgress = jest.fn();

jest.mock('../data/DebtStatisticsData', () => ({
  fetchDebtStatistics: mockFetchDebtStatistics,
  fetchDebtStatisticsDetail: mockFetchDebtStatisticsDetail,
  fetchDebtCollectionStatus: mockFetchDebtCollectionStatus,
  fetchDebtDisposalMethods: mockFetchDebtDisposalMethods,
  fetchCompanyRecoveryProgress: mockFetchCompanyRecoveryProgress
}));

describe('OverdueStatistics Integration Test', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup default mock responses
    mockFetchDebtStatistics.mockResolvedValue({
      totalReductionAmount: 1000,
      totalDebtBalance: 2000,
      initialDebtBalance: 3000,
      initialDebtReductionAmount: 400,
      initialDebtEndingBalance: 2600,
      newDebtAmount: 500,
      newDebtReductionAmount: 100,
      newDebtBalance: 400,
      newDebtSummaryByCompany: [],
      existingDebtSummaryByCompany: [],
      monthNewReductionDebtByCompany: []
    });

    mockFetchDebtStatisticsDetail.mockResolvedValue({
      newDebtDetailList: [],
      reductionDebtDetailList: []
    });

    mockFetchDebtCollectionStatus.mockResolvedValue({
      yearBeginAmount: 5000,
      monthDisposalAmount: 1000,
      yearCumulativeAmount: 3000,
      periodEndAmount: 2000
    });

    mockFetchDebtDisposalMethods.mockResolvedValue([
      { method: '现金清收', amount: 1000, percentage: 50 },
      { method: '资产处置', amount: 800, percentage: 40 },
      { method: '债务重组', amount: 200, percentage: 10 }
    ]);

    mockFetchCompanyRecoveryProgress.mockResolvedValue([
      { companyName: '公司A', yearEndAmount: 1000, actualAmount: 800, completionRate: 80 },
      { companyName: '公司B', yearEndAmount: 2000, actualAmount: 1500, completionRate: 75 }
    ]);
  });

  test('应该渲染所有三个新图表组件', async () => {
    render(<OverdueStatistics />);

    // 等待组件完成初始渲染和数据加载
    await waitFor(() => {
      expect(mockFetchDebtStatistics).toHaveBeenCalled();
      expect(mockFetchDebtStatisticsDetail).toHaveBeenCalled();
    });

    // 验证新图表组件是否渲染
    await waitFor(() => {
      const collectionChart = screen.getByTestId('collection-status-chart');
      expect(collectionChart).toBeInTheDocument();
      expect(collectionChart).toHaveTextContent('存量债权清收情况');

      const disposalChart = screen.getByTestId('disposal-methods-chart');
      expect(disposalChart).toBeInTheDocument();
      expect(disposalChart).toHaveTextContent('存量债权清收处置方式');

      const progressChart = screen.getByTestId('company-progress-chart');
      expect(progressChart).toBeInTheDocument();
      expect(progressChart).toHaveTextContent('各子公司存量债权回收完成情况');
    });
  });

  test('初始加载时不应调用新的API函数', async () => {
    render(<OverdueStatistics />);

    // 等待初始数据加载
    await waitFor(() => {
      expect(mockFetchDebtStatistics).toHaveBeenCalledTimes(1);
      expect(mockFetchDebtStatisticsDetail).toHaveBeenCalledTimes(1);
    });

    // 验证新API函数在初始加载时不被调用
    expect(mockFetchDebtCollectionStatus).not.toHaveBeenCalled();
    expect(mockFetchDebtDisposalMethods).not.toHaveBeenCalled();
    expect(mockFetchCompanyRecoveryProgress).not.toHaveBeenCalled();
  });

  test('API调用失败时应该优雅处理错误', async () => {
    // 模拟API调用失败
    mockFetchDebtCollectionStatus.mockRejectedValue(new Error('API Error'));
    mockFetchDebtDisposalMethods.mockRejectedValue(new Error('API Error'));
    mockFetchCompanyRecoveryProgress.mockRejectedValue(new Error('API Error'));

    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<OverdueStatistics />);

    // 等待组件渲染
    await waitFor(() => {
      expect(mockFetchDebtStatistics).toHaveBeenCalled();
    });

    // 验证错误被正确捕获和记录
    expect(consoleErrorSpy).toHaveBeenCalledWith('获取债权统计数据失败:', expect.any(String));

    consoleErrorSpy.mockRestore();
  });
});
