import React from 'react';
import CompanyProgressChart from './CompanyProgressChart';

/**
 * CompanyProgressChart 使用示例
 * 展示如何在实际页面中使用该组件
 */
const CompanyProgressChartExample = () => {
  // 模拟数据 - 实际使用时应从后端API获取
  const mockCompanyData = [
    {
      companyName: '中筑天佑',
      yearEndAmount: 11224.56,
      cumulativeRecovery: 10324.23,
      periodEndAmount: 900.33,
      completionRate: 103.2,
      targetAmount: 10000
    },
    {
      companyName: '贵州中恒',
      yearEndAmount: 8547.89,
      cumulativeRecovery: 7265.71,
      periodEndAmount: 1282.18,
      completionRate: 85.0,
      targetAmount: 8547.89
    },
    {
      companyName: '中建西南',
      yearEndAmount: 6823.45,
      cumulativeRecovery: 6141.11,
      periodEndAmount: 682.34,
      completionRate: 90.0,
      targetAmount: 6823.45
    },
    {
      companyName: '黔通建设',
      yearEndAmount: 5234.67,
      cumulativeRecovery: 4921.19,
      periodEndAmount: 313.48,
      completionRate: 94.0,
      targetAmount: 5234.67
    },
    {
      companyName: '天祥资产',
      yearEndAmount: 4567.89,
      cumulativeRecovery: 4340.5,
      periodEndAmount: 227.39,
      completionRate: 95.0,
      targetAmount: 4567.89
    },
    {
      companyName: '中恒投资',
      yearEndAmount: 3890.12,
      cumulativeRecovery: 3579.91,
      periodEndAmount: 310.21,
      completionRate: 92.0,
      targetAmount: 3890.12
    },
    {
      companyName: '贵州建工',
      yearEndAmount: 3456.78,
      cumulativeRecovery: 3318.51,
      periodEndAmount: 138.27,
      completionRate: 96.0,
      targetAmount: 3456.78
    },
    {
      companyName: '中筑设计',
      yearEndAmount: 2987.65,
      cumulativeRecovery: 2838.27,
      periodEndAmount: 149.38,
      completionRate: 95.0,
      targetAmount: 2987.65
    },
    {
      companyName: '黔中开发',
      yearEndAmount: 2345.67,
      cumulativeRecovery: 2088.45,
      periodEndAmount: 257.22,
      completionRate: 89.0,
      targetAmount: 2345.67
    },
    {
      companyName: '天成置业',
      yearEndAmount: 1987.34,
      cumulativeRecovery: 1827.95,
      periodEndAmount: 159.39,
      completionRate: 92.0,
      targetAmount: 1987.34
    }
  ];

  // 实际使用时的数据获取示例
  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       const response = await api.get('/debt/company-progress');
  //       setCompanyData(response.data);
  //     } catch (error) {
  //       console.error('获取公司进度数据失败:', error);
  //     }
  //   };
  //   fetchData();
  // }, []);

  return (
    <div style={{ padding: '20px' }}>
      <h2>组件使用示例</h2>

      {/* 基本使用 */}
      <CompanyProgressChart data={mockCompanyData} />

      {/* 自定义标题 */}
      <div style={{ marginTop: '40px' }}>
        <CompanyProgressChart
          data={mockCompanyData.slice(0, 5)}
          title="2024年度前五大公司债权回收进度"
        />
      </div>

      {/* 无数据情况 */}
      <div style={{ marginTop: '40px' }}>
        <CompanyProgressChart data={[]} title="暂无数据示例" />
      </div>
    </div>
  );
};

export default CompanyProgressChartExample;
