import React from 'react';
import PropTypes from 'prop-types';

const FormTextarea = ({ label, value, onChange, placeholder }) => {
  return (
    <div className="form-group" style={{ marginBottom: '15px' }}>
      <label>{label}</label>
      <textarea
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ddd' }}
      />
    </div>
  );
};

// 添加 PropTypes 校验
FormTextarea.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string
};

export default FormTextarea;
