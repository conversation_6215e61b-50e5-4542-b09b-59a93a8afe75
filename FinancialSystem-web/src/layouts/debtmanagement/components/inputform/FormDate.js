import React from 'react';
import PropTypes from 'prop-types';

const FormDate = ({ label, value, onChange, required, error }) => {
  return (
    <div className="form-group" style={{ marginBottom: '15px' }}>
      <label>
        {label} {required && <span style={{ color: 'red' }}>*</span>}
      </label>
      <input
        type="date"
        value={value}
        onChange={onChange}
        required={required}
        style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ddd' }}
      />
      {error && (
        <span className="error" style={{ color: 'red' }}>
          {error}
        </span>
      )}
    </div>
  );
};

// 🔹 添加 PropTypes
FormDate.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  required: PropTypes.bool,
  error: PropTypes.string
};

export default FormDate;
