import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Paper, Grid } from '@mui/material';

const FormDateSelect = ({ label, value, onChange, options, required, error }) => {
  // 使用state控制选项弹出框的显示
  const [showOptions, setShowOptions] = useState(false);

  // 创建ref来引用组件DOM元素
  const dateSelectRef = useRef(null);

  // 找到当前选中项的标签文本
  const selectedOption = options.find(opt => opt.value === value);
  const selectedLabel = selectedOption ? selectedOption.label : '请选择日期';

  // 处理点击外部区域关闭选项框
  useEffect(() => {
    function handleClickOutside(event) {
      if (dateSelectRef.current && !dateSelectRef.current.contains(event.target)) {
        setShowOptions(false);
      }
    }

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理选项点击
  const handleOptionClick = optionValue => {
    // 创建一个模拟的事件对象，以便与原有的onChange处理兼容
    const mockEvent = {
      target: { value: optionValue }
    };
    onChange(mockEvent);
    setShowOptions(false);
  };

  return (
    <div
      className="form-group"
      style={{ marginBottom: '15px', position: 'relative' }}
      ref={dateSelectRef}
    >
      <label>
        {label} {required && <span style={{ color: 'red' }}>*</span>}
      </label>
      <div
        onClick={() => setShowOptions(!showOptions)}
        onKeyPress={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            setShowOptions(!showOptions);
          }
        }}
        tabIndex="0"
        role="button"
        aria-haspopup="true"
        aria-expanded={showOptions}
        style={{
          width: '100%',
          padding: '10px 15px',
          borderRadius: '4px',
          border: '1px solid #ddd',
          backgroundColor: '#fff',
          cursor: 'pointer',
          minHeight: '42px',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        {selectedLabel}
      </div>

      {showOptions && (
        <Paper
          elevation={3}
          style={{
            position: 'absolute',
            width: '100%',
            zIndex: 1000,
            marginTop: '2px',
            maxHeight: '200px',
            overflowY: 'auto'
          }}
        >
          <Grid container spacing={0} role="menu">
            {options.map(option => (
              <Grid item xs={12} key={`date-option-${option.value}`}>
                <Box
                  sx={{
                    padding: '10px 15px',
                    borderBottom: '1px solid #eee',
                    cursor: 'pointer',
                    '&:hover': { backgroundColor: '#f5f5f5' },
                    backgroundColor: option.value === value ? '#e3f2fd' : 'transparent'
                  }}
                  role="menuitem"
                  tabIndex="0"
                  onKeyPress={e => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      handleOptionClick(option.value);
                    }
                  }}
                  onClick={() => handleOptionClick(option.value)}
                >
                  {option.label}
                </Box>
              </Grid>
            ))}
          </Grid>
        </Paper>
      )}

      {error && (
        <span className="error" style={{ color: 'red' }}>
          {error}
        </span>
      )}
    </div>
  );
};

// 添加 PropTypes 校验
FormDateSelect.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.array.isRequired,
  required: PropTypes.bool,
  error: PropTypes.string
};

export default FormDateSelect;
