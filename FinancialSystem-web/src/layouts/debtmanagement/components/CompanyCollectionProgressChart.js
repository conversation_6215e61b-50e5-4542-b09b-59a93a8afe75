import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, FormControl, InputLabel, Select, MenuItem, Box, Typography, Chip, Button } from '@mui/material';
import { Download } from '@mui/icons-material';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

/**
 * 各子公司年度清收目标完成情况图表组件
 * 显示堆叠柱状图（累计清收金额+未完成金额）和完成进度折线图
 */
const CompanyCollectionProgressChart = ({ year, month, onExport }) => {
  // 状态管理
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [displayMode, setDisplayMode] = useState('TARGET'); // TARGET或INITIAL

  // 获取数据
  const fetchData = async () => {
    if (!year || !month) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/debts/statistics/company-collection-progress?year=${year}&month=${month}&displayMode=${displayMode}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      setData(result.data || []);
    } catch (err) {
      console.error('获取公司清收进度数据失败:', err);
      setError(err.message || '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 监听参数变化
  useEffect(() => {
    fetchData();
  }, [year, month, displayMode]);

  // 处理展示模式切换
  const handleDisplayModeChange = (event) => {
    setDisplayMode(event.target.value);
  };

  // 处理Excel导出
  const handleExport = async () => {
    if (!year || !month) {
      alert('请先设置年份和月份');
      return;
    }

    try {
      const response = await fetch(
        `/api/debts/export/company-collection-progress?year=${year}&month=${month}&displayMode=${displayMode}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // 获取文件名
      const contentDisposition = response.headers.get('content-disposition');
      const fileNameMatch = contentDisposition && contentDisposition.match(/filename="(.+)"/);
      const fileName = fileNameMatch ? fileNameMatch[1] : 
        `各子公司年度清收目标完成情况_${year}年${month}_${displayMode === 'TARGET' ? '按目标' : '按期初'}.xlsx`;
      
      // 下载文件
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
    } catch (err) {
      console.error('导出Excel失败:', err);
      alert('导出失败: ' + (err.message || '未知错误'));
    }
  };

  // 图表数据处理
  const chartData = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        labels: [],
        datasets: []
      };
    }

    // 排序：按完成率降序
    const sortedData = [...data].sort((a, b) => b.completionRate - a.completionRate);

    const labels = sortedData.map(item => item.managementCompany);
    const cumulativeAmounts = sortedData.map(item => parseFloat(item.cumulativeCollectionAmount) || 0);
    const remainingAmounts = sortedData.map(item => parseFloat(item.remainingAmount) || 0);
    const completionRates = sortedData.map(item => parseFloat(item.completionRate) || 0);

    return {
      labels,
      datasets: [
        {
          label: '累计清收金额(万元)',
          data: cumulativeAmounts,
          backgroundColor: '#4CAF50', // 绿色
          borderColor: '#4CAF50',
          borderWidth: 1,
          stack: 'stack1'
        },
        {
          label: '未完成金额(万元)',
          data: remainingAmounts,
          backgroundColor: '#f44336', // 红色
          borderColor: '#f44336',
          borderWidth: 1,
          stack: 'stack1'
        }
      ]
    };
  }, [data]);

  // 图表配置
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: true,
        text: `各子公司年度清收目标完成情况 (${displayMode === 'TARGET' ? '按清收目标' : '按期初金额'})`
      },
      tooltip: {
        callbacks: {
          title: (tooltipItems) => {
            const index = tooltipItems[0]?.dataIndex;
            if (index !== undefined && data[index]) {
              return data[index].managementCompany;
            }
            return '';
          },
          footer: (tooltipItems) => {
            const index = tooltipItems[0]?.dataIndex;
            if (index !== undefined && data[index]) {
              const item = data[index];
              return [
                `完成进度: ${item.formattedCompletionRate || '0.0%'}`,
                displayMode === 'TARGET' 
                  ? `清收目标: ${item.formattedTargetAmount || '0'}万元`
                  : `期初金额: ${item.formattedYearBeginAmount || '0'}万元`
              ];
            }
            return [];
          }
        }
      },
      legend: {
        display: true,
        position: 'top'
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: '管理公司'
        },
        ticks: {
          maxRotation: 45,
          minRotation: 0
        }
      },
      y: {
        title: {
          display: true,
          text: '金额(万元)'
        },
        stacked: true,
        beginAtZero: true
      }
    }
  };

  // 统计信息
  const statistics = useMemo(() => {
    if (!data || data.length === 0) return null;

    const totalBenchmark = data.reduce((sum, item) => 
      sum + (parseFloat(item.benchmarkAmount) || 0), 0);
    const totalCollection = data.reduce((sum, item) => 
      sum + (parseFloat(item.cumulativeCollectionAmount) || 0), 0);
    const averageRate = data.length > 0 
      ? data.reduce((sum, item) => sum + (parseFloat(item.completionRate) || 0), 0) / data.length
      : 0;

    return {
      totalBenchmark,
      totalCollection,
      averageRate,
      companyCount: data.length
    };
  }, [data]);

  // 渲染加载状态
  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" height={400}>
            <Typography>加载中...</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" height={400}>
            <Typography color="error">加载失败: {error}</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  // 渲染无数据状态
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" flexDirection="column" gap={2}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h6">各子公司年度清收目标完成情况</Typography>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>展示模式</InputLabel>
                <Select
                  value={displayMode}
                  label="展示模式"
                  onChange={handleDisplayModeChange}
                >
                  <MenuItem value="TARGET">按清收目标</MenuItem>
                  <MenuItem value="INITIAL">按期初金额</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Box display="flex" justifyContent="center" alignItems="center" height={400}>
              <Typography color="textSecondary">暂无数据</Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" flexDirection="column" gap={2}>
          {/* 头部控制区域 */}
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">各子公司年度清收目标完成情况</Typography>
            <Box display="flex" gap={1} alignItems="center">
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>展示模式</InputLabel>
                <Select
                  value={displayMode}
                  label="展示模式"
                  onChange={handleDisplayModeChange}
                >
                  <MenuItem value="TARGET">按清收目标</MenuItem>
                  <MenuItem value="INITIAL">按期初金额</MenuItem>
                </Select>
              </FormControl>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Download />}
                onClick={handleExport}
                disabled={!data || data.length === 0}
              >
                导出Excel
              </Button>
            </Box>
          </Box>

          {/* 统计信息卡片 */}
          {statistics && (
            <Box display="flex" gap={2} flexWrap="wrap">
              <Chip 
                label={`公司数量: ${statistics.companyCount}个`}
                color="primary"
                variant="outlined"
              />
              <Chip 
                label={`${displayMode === 'TARGET' ? '目标' : '期初'}总额: ${statistics.totalBenchmark.toLocaleString()}万元`}
                color="info"
                variant="outlined"
              />
              <Chip 
                label={`累计清收: ${statistics.totalCollection.toLocaleString()}万元`}
                color="success"
                variant="outlined"
              />
              <Chip 
                label={`平均完成率: ${statistics.averageRate.toFixed(1)}%`}
                color={statistics.averageRate >= 50 ? "success" : "warning"}
                variant="outlined"
              />
            </Box>
          )}

          {/* 图表区域 */}
          <Box height={500}>
            <Bar data={chartData} options={chartOptions} />
          </Box>

          {/* 表格数据区域 */}
          <Box sx={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse', marginTop: 16 }}>
              <thead>
                <tr style={{ backgroundColor: '#f5f5f5' }}>
                  <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center' }}>排名</th>
                  <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left' }}>管理公司</th>
                  <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>期初金额(万元)</th>
                  <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>清收目标(万元)</th>
                  <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>累计清收(万元)</th>
                  <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>未完成(万元)</th>
                  <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>完成进度</th>
                  <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center' }}>状态</th>
                </tr>
              </thead>
              <tbody>
                {[...data]
                  .sort((a, b) => b.completionRate - a.completionRate)
                  .map((item, index) => (
                    <tr key={item.managementCompany} style={{ 
                      backgroundColor: index % 2 === 0 ? '#fafafa' : 'white' 
                    }}>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center' }}>
                        {index + 1}
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>
                        {item.managementCompany}
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>
                        {item.formattedYearBeginAmount || '0'}
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>
                        {item.hasTarget ? (item.formattedTargetAmount || '0') : '-'}
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>
                        {item.formattedCumulativeAmount || '0'}
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>
                        {item.formattedRemainingAmount || '0'}
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right' }}>
                        <span style={{ 
                          color: item.completionRate >= 80 ? '#4CAF50' : 
                                 item.completionRate >= 50 ? '#FF9800' : '#f44336',
                          fontWeight: 'bold'
                        }}>
                          {item.formattedCompletionRate || '0.0%'}
                        </span>
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center' }}>
                        {item.completionRate >= 100 ? (
                          <Chip label="已完成" color="success" size="small" />
                        ) : item.completionRate >= 80 ? (
                          <Chip label="良好" color="info" size="small" />
                        ) : item.completionRate >= 50 ? (
                          <Chip label="一般" color="warning" size="small" />
                        ) : (
                          <Chip label="较差" color="error" size="small" />
                        )}
                      </td>
                    </tr>
                  ))
                }
              </tbody>
            </table>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default CompanyCollectionProgressChart;