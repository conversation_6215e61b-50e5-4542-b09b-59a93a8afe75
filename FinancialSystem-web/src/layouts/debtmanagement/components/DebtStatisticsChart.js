import React from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Bar } from 'react-chartjs-2';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 逾期债权统计图组件（按公司分类）
 * @param {Object} props
 * @param {Array} props.data - 数据数组，每项包含 { companyName, newAmountSum, reductionAmountSum }
 * @param {String} props.chartTitle - 图表标题
 * @param {String} props.chartDebtLabel - 第一个柱状图的标签
 * @returns {JSX.Element}
 */
const DebtStatisticsChart = ({ data, chartTitle, chartDebtLabel }) => {
  // 1. 根据 newAmountSum 做降序
  const sortedData = [...data].sort((a, b) => b.newAmountSum - a.newAmountSum);

  // 2. 拿到公司名称数组
  const labels = sortedData.map(item => item.companyName);

  // 3. 组装 chart.js 数据结构
  const chartData = {
    labels,
    datasets: [
      {
        label: chartDebtLabel,
        data: sortedData.map(item => item.newAmountSum),
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderWidth: 1,
        yAxisID: 'y'
      },
      {
        label: '处置逾期债权',
        data: sortedData.map(item => item.reductionAmountSum),
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderWidth: 1,
        yAxisID: 'y'
      },
      {
        label: '完成百分比',
        data: sortedData.map(item => {
          const 新增金额 = item.newAmountSum || 0;
          const 处置金额 = item.reductionAmountSum || 0;
          return 新增金额 !== 0 ? (处置金额 / 新增金额) * 100 : 0;
        }),
        type: 'line',
        borderColor: 'rgba(54, 162, 235, 1)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderWidth: 2,
        yAxisID: 'y1'
      }
    ]
  };

  // 4. 配置图表选项
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top'
      },
      // 从传入的 chartTitle 中读取标题
      title: {
        display: true,
        text: chartTitle
      },
      datalabels: {
        display(context) {
          // 如果是折线图，就不显示数据标签
          return context.dataset.type !== 'line';
          // 否则，柱状图继续显示
        },
        anchor: 'end',
        align: 'start',
        offset: -5,
        color: '#000',
        font: {
          // weight: 'bold',
          size: 9
        },
        formatter(value, context) {
          // 柱状图显示整数
          return Math.round(value).toLocaleString();
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          maxRotation: labels.length > 8 ? 45 : 0,
          minRotation: labels.length > 8 ? 45 : 0,
          autoSkip: false,
          font: {
            size: 11
          }
        }
      },
      y: {
        type: 'linear',
        position: 'left',
        title: {
          display: true,
          text: '金额 (万元)'
        }
      },
      y1: {
        type: 'linear',
        position: 'right',
        title: {
          display: true,
          text: '完成百分比 (%)',
          font: {
            weight: 'bold', // 让标题加粗
            size: 14 // 可以调整字体大小
          }
        },
        grid: {
          drawOnChartArea: false
        },
        ticks: {
          // callback: (value) => `${value}%`,
          font: {
            weight: 'bold', // 让 Y 轴上的百分比数据加粗
            size: 12 // 调整字体大小
          }
        }
      }
    },
    layout: {
      padding: {
        left: 20,
        right: 40,
        top: 20,
        bottom: 20
      }
    }
  };

  return (
    <div
      style={{
        minWidth: labels.length > 8 ? '900px' : '650px',
        width: '100%',
        maxWidth: '100%',
        height: '400px',
        position: 'relative',
        overflowX: 'auto',
        overflowY: 'hidden',
        padding: '0',
        margin: '0 auto'
      }}
      className="p-6 bg-white rounded-lg shadow-md"
    >
      <Bar
        data={chartData}
        options={options}
        style={{
          height: '100%',
          width: '100%',
          minHeight: '300px'
        }}
      />
    </div>
  );
};

DebtStatisticsChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      companyName: PropTypes.string.isRequired, // 管理公司名称
      newAmountSum: PropTypes.number.isRequired, // 新增金额
      reductionAmountSum: PropTypes.number.isRequired // 处置金额
    })
  ).isRequired,
  // 图表标题
  chartTitle: PropTypes.string.isRequired,
  // 第一个柱状图的标签
  chartDebtLabel: PropTypes.string.isRequired
};

export default DebtStatisticsChart;
