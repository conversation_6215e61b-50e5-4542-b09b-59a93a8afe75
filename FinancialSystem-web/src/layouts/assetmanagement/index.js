import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  Grid,
  Divider,
  Alert,
  CircularProgress,
  Button
} from '@mui/material';
import {
  Description as DescriptionIcon,
  Assessment as AssessmentIcon,
  Home as HomeIcon,
  Group as GroupIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import { styles } from './styles';
import { mockData } from './data/mockData';
import AssetStatistics from './components/AssetStatistics';
import AssetDetails from './components/AssetDetails';
import GroupAffairsCard from './components/GroupAffairsCard';
import AddItemDialog from './components/AddItemDialog';
import NotificationSnackbar from './components/NotificationSnackbar';
import AssetAreaPieChart from './components/AssetAreaPieChart';
import AssetActivationBar<PERSON>hart from './components/AssetActivationBarChart';
import DefectiveAssetCard from './components/DefectiveAssetCard';
import {
  getAssets,
  testAssetAPI,
  getManagementCompanies,
  createAsset,
  updateAsset,
  deleteAsset,
  getAreaDistribution,
  getCompanyActivationStatistics,
  getDefectiveAssetStatistics
} from './services/assetService';

const AssetManagement = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedModule, setSelectedModule] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    company: '',
    description: ''
  });

  // API数据状态
  const [assets, setAssets] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [apiConnected, setApiConnected] = useState(false);
  const [managementCompanies, setManagementCompanies] = useState([]);
  const [currentCompany, setCurrentCompany] = useState('深圳万润科技股份有限公司');

  // 图表数据状态
  const [areaDistribution, setAreaDistribution] = useState(null);
  const [companyActivationStats, setCompanyActivationStats] = useState([]);
  const [defectiveAssetStats, setDefectiveAssetStats] = useState([]);

  const handleTabChange = (_, newValue) => {
    setSelectedTab(newValue);
  };

  const handleOpenDialog = module => {
    setSelectedModule(module);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setFormData({ title: '', company: '', description: '' });
  };

  const handleSubmit = () => {
    // 这里添加提交逻辑
    handleCloseDialog();
  };

  // 数据转换函数：将API数据转换为组件需要的格式
  const transformApiDataToStatistics = apiAssets => {
    const properties = apiAssets.filter(asset => asset.assetType === 'PROPERTY');
    const lands = apiAssets.filter(asset => asset.assetType === 'LAND');

    const totalArea = apiAssets.reduce((sum, asset) => sum + (parseFloat(asset.totalArea) || 0), 0);
    const totalValue = apiAssets.reduce(
      (sum, asset) => sum + (parseFloat(asset.purchasePrice) || 0),
      0
    );
    const selfUseArea = apiAssets.reduce(
      (sum, asset) => sum + (parseFloat(asset.currentSelfUseArea) || 0),
      0
    );
    const rentalArea = apiAssets.reduce(
      (sum, asset) => sum + (parseFloat(asset.currentRentalArea) || 0),
      0
    );

    return {
      totalArea,
      totalValue,
      selfUseArea,
      rentalArea,
      properties: properties.map(asset => ({
        id: asset.id,
        name: asset.assetName,
        type: asset.assetTypeDescription || '房产',
        area: parseFloat(asset.totalArea) || 0,
        purchasePrice: parseFloat(asset.purchasePrice) || 0,
        selfUseArea: Math.floor((parseFloat(asset.totalArea) || 0) * 0.7),
        rentalArea: Math.floor((parseFloat(asset.totalArea) || 0) * 0.3),
        location: asset.location,
        propertyCertificateNo: asset.propertyCertificateNo,
        hasPropertyCertificate: asset.hasPropertyCertificate
      })),
      lands: lands.map(asset => ({
        id: asset.id,
        name: asset.assetName,
        type: asset.assetTypeDescription || '土地',
        area: parseFloat(asset.totalArea) || 0,
        purchasePrice: parseFloat(asset.purchasePrice) || 0,
        selfUseArea: Math.floor((parseFloat(asset.totalArea) || 0) * 0.3),
        rentalArea: Math.floor((parseFloat(asset.totalArea) || 0) * 0.7),
        location: asset.location,
        propertyCertificateNo: asset.propertyCertificateNo,
        hasPropertyCertificate: asset.hasPropertyCertificate
      }))
    };
  };

  // 加载资产数据
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // 首先测试API连接
      await testAssetAPI();
      setApiConnected(true);

      // 获取管理公司列表
      try {
        const companiesResponse = await getManagementCompanies();
        setManagementCompanies(companiesResponse || []);
      } catch (companiesError) {
        console.warn('获取管理公司列表失败:', companiesError);
      }

      // 获取资产列表
      const assetsResponse = await getAssets({
        managementCompany: currentCompany,
        page: 0,
        size: 100
      });
      const apiAssets = assetsResponse.content || [];
      setAssets(apiAssets);

      // 转换数据为统计格式
      const transformedStats = transformApiDataToStatistics(apiAssets);
      setStatistics(transformedStats);

      // 加载图表数据
      try {
        const areaDistData = await getAreaDistribution(currentCompany);
        setAreaDistribution(areaDistData);
      } catch (error) {
        console.warn('获取面积分布数据失败:', error);
      }

      try {
        const activationData = await getCompanyActivationStatistics();
        setCompanyActivationStats(activationData);
      } catch (error) {
        console.warn('获取公司盘活统计失败:', error);
      }

      try {
        const defectiveData = await getDefectiveAssetStatistics();
        setDefectiveAssetStats(defectiveData);
      } catch (error) {
        console.warn('获取瑕疵资产统计失败:', error);
      }
    } catch (error) {
      console.error('加载资产数据失败:', error);
      setError('无法连接到资产管理API，将使用模拟数据');
      setApiConnected(false);
      // 使用模拟数据作为后备
      setAssets(mockData.companies[0].assets.properties.concat(mockData.companies[0].assets.lands));
      setStatistics(mockData.companies[0].assets);
    } finally {
      setLoading(false);
    }
  }, [currentCompany]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // 资产操作处理函数
  const handleAssetCreate = async assetData => {
    try {
      await createAsset(assetData);
      await loadData(); // 重新加载数据
    } catch (error) {
      throw new Error(error.response?.data?.message || '创建资产失败');
    }
  };

  const handleAssetUpdate = async (id, assetData) => {
    try {
      await updateAsset(id, assetData);
      await loadData(); // 重新加载数据
    } catch (error) {
      throw new Error(error.response?.data?.message || '更新资产失败');
    }
  };

  const handleAssetDelete = async id => {
    try {
      await deleteAsset(id);
      await loadData(); // 重新加载数据
    } catch (error) {
      throw new Error(error.response?.data?.message || '删除资产失败');
    }
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="lg">
        <Box sx={styles.root}>
          {/* API连接状态提示 */}
          {error && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {apiConnected && (
            <Alert severity="success" sx={{ mb: 2 }}>
              已成功连接到资产管理API，显示真实数据
            </Alert>
          )}

          {/* 加载状态 */}
          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
              <Typography sx={{ ml: 2 }}>正在加载资产数据...</Typography>
            </Box>
          )}

          {/* 资产整理情况部分 */}
          {!loading && (
            <Paper sx={styles.paper} elevation={0}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 2
                }}
              >
                <Box>
                  <Typography variant="h4" sx={styles.title}>
                    公司资产整理情况
                  </Typography>
                  {apiConnected && currentCompany && (
                    <Typography variant="subtitle1" sx={{ mt: 1, color: 'text.secondary' }}>
                      管理公司：{currentCompany}
                    </Typography>
                  )}
                </Box>
                <Button
                  variant="contained"
                  startIcon={<RefreshIcon />}
                  onClick={loadData}
                  disabled={loading}
                  sx={{
                    backgroundColor: '#ffffff',
                    color: loading ? '#ff9800' : '#1976d2',
                    border: loading ? '2px solid #ff9800' : '2px solid #1976d2',
                    '&:hover': {
                      backgroundColor: '#f5f5f5',
                      borderColor: loading ? '#f57c00' : '#1565c0'
                    },
                    '&:disabled': {
                      backgroundColor: '#ffffff',
                      color: '#bdbdbd',
                      borderColor: '#bdbdbd'
                    },
                    fontWeight: 600,
                    boxShadow: loading
                      ? '0 2px 8px rgba(255, 152, 0, 0.3)'
                      : '0 2px 8px rgba(25, 118, 210, 0.3)',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {loading ? '刷新中...' : '刷新数据'}
                </Button>
              </Box>
              <Divider sx={{ mb: 4 }} />

              <AssetStatistics
                assets={statistics || mockData.companies[0].assets}
                loading={loading}
                apiConnected={apiConnected}
              />

              {/* 数据可视化图表区域 */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={6}>
                  <AssetAreaPieChart
                    data={
                      areaDistribution || {
                        selfUseArea: (statistics && statistics.selfUseArea) || 20000,
                        rentalArea: (statistics && statistics.rentalArea) || 15000,
                        idleArea: 0
                      }
                    }
                    title="资产面积分布"
                    loading={loading}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <DefectiveAssetCard
                    data={defectiveAssetStats}
                    title="瑕疵资产统计"
                    loading={loading}
                  />
                </Grid>
              </Grid>

              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12}>
                  <AssetActivationBarChart
                    data={companyActivationStats}
                    title="各公司资产盘活情况"
                    loading={loading}
                  />
                </Grid>
              </Grid>

              <AssetDetails
                selectedTab={selectedTab}
                handleTabChange={handleTabChange}
                properties={
                  (statistics && statistics.properties) || mockData.companies[0].assets.properties
                }
                lands={(statistics && statistics.lands) || mockData.companies[0].assets.lands}
                loading={loading}
                apiConnected={apiConnected}
                onAssetCreate={handleAssetCreate}
                onAssetUpdate={handleAssetUpdate}
                onAssetDelete={handleAssetDelete}
              />
            </Paper>
          )}

          {/* 对接集团事项部分 */}
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h4" sx={styles.title}>
              对接集团事项
            </Typography>
            <Divider sx={{ mb: 4 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <GroupAffairsCard
                  title="资产报备"
                  icon={<DescriptionIcon color="primary" />}
                  items={mockData.assetReporting}
                  module="assetReporting"
                  onAddClick={handleOpenDialog}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <GroupAffairsCard
                  title="评估备案"
                  icon={<AssessmentIcon color="primary" />}
                  items={mockData.evaluationRecords}
                  module="evaluationRecords"
                  onAddClick={handleOpenDialog}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <GroupAffairsCard
                  title="产权登记"
                  icon={<HomeIcon color="primary" />}
                  items={mockData.propertyRegistration}
                  module="propertyRegistration"
                  onAddClick={handleOpenDialog}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <GroupAffairsCard
                  title="资产评审委员会"
                  icon={<GroupIcon color="primary" />}
                  items={mockData.reviewCommittee}
                  module="reviewCommittee"
                  onAddClick={handleOpenDialog}
                />
              </Grid>
            </Grid>
          </Paper>
        </Box>
      </Container>

      <AddItemDialog
        open={openDialog}
        onClose={handleCloseDialog}
        selectedModule={selectedModule}
        formData={formData}
        onFormChange={setFormData}
        onSubmit={handleSubmit}
      />
    </DashboardLayout>
  );
};

export default AssetManagement;
