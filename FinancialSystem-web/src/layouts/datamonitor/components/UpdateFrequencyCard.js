import React from 'react';
import PropTypes from 'prop-types';
import { Box, Card, CardContent, Typography, Avatar, Divider } from '@mui/material';
import { Event as EventIcon } from '@mui/icons-material';

const UpdateFrequencyCard = ({ title, icon, frequency, responsiblePerson, lastUpdate, type }) => {
  // 根据类型决定颜色
  const getColor = type => {
    if (type === '非诉讼') {
      return '#e91e63'; // 粉色
    } else {
      return '#2196f3'; // 蓝色
    }
  };

  const color = getColor(type);

  // 样式
  const styles = {
    card: {
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      borderRadius: 2,
      boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
      transition: 'transform 0.2s',
      '&:hover': {
        transform: 'translateY(-5px)',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
      }
    },
    header: {
      display: 'flex',
      alignItems: 'center',
      mb: 3
    },
    avatar: {
      mr: 2,
      bgcolor: color,
      width: 50,
      height: 50
    },
    title: {
      fontWeight: 600
    },
    infoRow: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      my: 1.5
    },
    label: {
      color: 'text.secondary',
      fontSize: '0.875rem'
    },
    value: {
      fontWeight: 500
    },
    lastUpdate: {
      display: 'flex',
      alignItems: 'center',
      mt: 3,
      color: 'text.secondary'
    },
    lastUpdateIcon: {
      fontSize: '1rem',
      mr: 0.5
    }
  };

  return (
    <Card sx={styles.card}>
      <CardContent>
        <Box sx={styles.header}>
          <Avatar sx={styles.avatar}>{icon}</Avatar>
          <Typography variant="h6" sx={styles.title}>
            {title}
          </Typography>
        </Box>

        <Box sx={styles.infoRow}>
          <Typography sx={styles.label}>更新频率</Typography>
          <Typography sx={styles.value} color={color}>
            {frequency}
          </Typography>
        </Box>

        <Divider />

        <Box sx={styles.infoRow}>
          <Typography sx={styles.label}>负责人</Typography>
          <Typography sx={styles.value}>{responsiblePerson}</Typography>
        </Box>

        <Box sx={styles.lastUpdate}>
          <EventIcon sx={styles.lastUpdateIcon} />
          <Typography variant="caption">上次更新: {lastUpdate}</Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

UpdateFrequencyCard.propTypes = {
  title: PropTypes.string.isRequired,
  icon: PropTypes.node.isRequired,
  frequency: PropTypes.string.isRequired,
  responsiblePerson: PropTypes.string.isRequired,
  lastUpdate: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired
};

export default UpdateFrequencyCard;
