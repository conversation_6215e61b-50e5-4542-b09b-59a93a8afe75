import React, { useState, useEffect } from 'react';
import { Card, Grid, Alert, CircularProgress, Drawer, IconButton, Chip } from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  CloudDownload as CloudDownloadIcon,
  History as HistoryIcon,
  Home as HomeIcon,
  KeyboardArrowRight as ArrowIcon,
  FileDownload as FileDownloadIcon,
  Close as CloseIcon
} from '@mui/icons-material';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';

// Material Dashboard 2 React example components
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';

// 数据导出中心组件
import OverdueDebtExportRow from '../components/OverdueDebtExportRow';
import ManagementBoardExportRow from '../components/ManagementBoardExportRow';

/**
 * 数据导出中心页面
 * 提供各种报表的导出功能，采用现代化美观界面设计
 */
const DataExportCenter = () => {
  const [historyOpen, setHistoryOpen] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [exportStats] = useState({
    monthlyCount: 24,
    totalSize: '156MB',
    successRate: '98.5%',
    avgTime: '2.3s'
  });

  // 模拟导出历史数据
  const [exportHistory] = useState([
    {
      id: 1,
      filename: '逾期债权明细表_2024年7月.xlsx',
      exportTime: '2024-07-09 14:30:25',
      dateRange: '2024年7月',
      recordCount: 1250,
      status: '成功'
    },
    {
      id: 2,
      filename: '逾期债权明细表_2024年6月.xlsx',
      exportTime: '2024-07-08 16:45:12',
      dateRange: '2024年6月',
      recordCount: 1180,
      status: '成功'
    },
    {
      id: 3,
      filename: '逾期债权明细表_2024年5月.xlsx',
      exportTime: '2024-07-07 09:15:33',
      dateRange: '2024年5月',
      recordCount: 1095,
      status: '失败'
    }
  ]);

  const handleRedownload = id => {
    console.log('重新下载:', id);
    // 这里可以调用重新下载的API
  };

  useEffect(() => {
    // 添加加载动画延迟
    const timer = setTimeout(() => setLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox pt={3} pb={3}>
        {/* 页面头部 */}
        <MDBox pt={3} pb={2}>
          <MDBox display="flex" alignItems="center" justifyContent="space-between">
            <MDBox>
              <MDTypography variant="h4" fontWeight="bold" color="dark">
                数据导出中心
              </MDTypography>
              <MDBox display="flex" alignItems="center" mt={1}>
                <HomeIcon sx={{ color: 'info.main', mr: 1 }} />
                <MDTypography variant="button" color="text" sx={{ mr: 1 }}>
                  首页
                </MDTypography>
                <ArrowIcon sx={{ color: 'text.main', fontSize: '16px', mr: 1 }} />
                <MDTypography variant="button" color="info" fontWeight="regular">
                  数据导出中心
                </MDTypography>
              </MDBox>
            </MDBox>
            <MDBox>
              <MDButton
                variant="gradient"
                color="info"
                size="small"
                onClick={() => setHistoryOpen(true)}
              >
                <HistoryIcon sx={{ mr: 1 }} />
                导出历史
              </MDButton>
            </MDBox>
          </MDBox>
        </MDBox>

        {/* 统计概览卡片 */}
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} md={3}>
            <MDBox
              bgColor="primary"
              variant="gradient"
              borderRadius="lg"
              coloredShadow="primary"
              p={2}
            >
              <MDBox display="flex" alignItems="center">
                <AssessmentIcon sx={{ color: 'white', fontSize: '48px', mr: 2 }} />
                <MDBox>
                  <MDTypography variant="h6" color="white">
                    本月导出
                  </MDTypography>
                  <MDTypography variant="h4" color="white" fontWeight="bold">
                    {exportStats.monthlyCount}
                  </MDTypography>
                </MDBox>
              </MDBox>
            </MDBox>
          </Grid>

          <Grid item xs={12} md={3}>
            <MDBox
              bgColor="success"
              variant="gradient"
              borderRadius="lg"
              coloredShadow="success"
              p={2}
            >
              <MDBox display="flex" alignItems="center">
                <CloudDownloadIcon sx={{ color: 'white', fontSize: '48px', mr: 2 }} />
                <MDBox>
                  <MDTypography variant="h6" color="white">
                    累计大小
                  </MDTypography>
                  <MDTypography variant="h4" color="white" fontWeight="bold">
                    {exportStats.totalSize}
                  </MDTypography>
                </MDBox>
              </MDBox>
            </MDBox>
          </Grid>

          <Grid item xs={12} md={3}>
            <MDBox
              bgColor="warning"
              variant="gradient"
              borderRadius="lg"
              coloredShadow="warning"
              p={2}
            >
              <MDBox display="flex" alignItems="center">
                <TrendingUpIcon sx={{ color: 'white', fontSize: '48px', mr: 2 }} />
                <MDBox>
                  <MDTypography variant="h6" color="white">
                    成功率
                  </MDTypography>
                  <MDTypography variant="h4" color="white" fontWeight="bold">
                    {exportStats.successRate}
                  </MDTypography>
                </MDBox>
              </MDBox>
            </MDBox>
          </Grid>

          <Grid item xs={12} md={3}>
            <MDBox bgColor="error" variant="gradient" borderRadius="lg" coloredShadow="error" p={2}>
              <MDBox display="flex" alignItems="center">
                <AnalyticsIcon sx={{ color: 'white', fontSize: '48px', mr: 2 }} />
                <MDBox>
                  <MDTypography variant="h6" color="white">
                    平均耗时
                  </MDTypography>
                  <MDTypography variant="h4" color="white" fontWeight="bold">
                    {exportStats.avgTime}
                  </MDTypography>
                </MDBox>
              </MDBox>
            </MDBox>
          </Grid>
        </Grid>

        {/* 主要导出功能 */}
        <MDBox mb={3}>
          <OverdueDebtExportRow />
        </MDBox>

        {/* 经营调度会看板导出 */}
        <MDBox mb={3}>
          <ManagementBoardExportRow />
        </MDBox>

        {/* 导出历史侧边栏 */}
        <Drawer
          anchor="right"
          open={historyOpen}
          onClose={() => setHistoryOpen(false)}
          PaperProps={{
            sx: { width: '400px', borderRadius: '12px 0 0 12px' }
          }}
        >
          <MDBox p={3}>
            <MDBox display="flex" alignItems="center" justifyContent="space-between" mb={3}>
              <MDTypography variant="h6" fontWeight="bold">
                导出历史
              </MDTypography>
              <IconButton onClick={() => setHistoryOpen(false)}>
                <CloseIcon />
              </IconButton>
            </MDBox>

            {exportHistory.map((item, index) => (
              <Card key={index} sx={{ mb: 2, borderRadius: '8px' }}>
                <MDBox p={2}>
                  <MDBox display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                    <MDTypography variant="subtitle2" fontWeight="bold">
                      {item.filename}
                    </MDTypography>
                    <Chip
                      label={item.status}
                      color={item.status === '成功' ? 'success' : 'error'}
                      size="small"
                    />
                  </MDBox>

                  <MDTypography variant="caption" color="text" display="block">
                    导出时间：{item.exportTime}
                  </MDTypography>
                  <MDTypography variant="caption" color="text" display="block">
                    数据范围：{item.dateRange}
                  </MDTypography>
                  <MDTypography variant="caption" color="text" display="block">
                    记录数量：{item.recordCount} 条
                  </MDTypography>

                  {item.status === '成功' && (
                    <MDBox mt={1}>
                      <MDButton
                        variant="text"
                        color="info"
                        size="small"
                        onClick={() => handleRedownload(item.id)}
                      >
                        <FileDownloadIcon sx={{ mr: 1, fontSize: '16px' }} />
                        重新下载
                      </MDButton>
                    </MDBox>
                  )}
                </MDBox>
              </Card>
            ))}
          </MDBox>
        </Drawer>
      </MDBox>
    </DashboardLayout>
  );
};

export default DataExportCenter;
