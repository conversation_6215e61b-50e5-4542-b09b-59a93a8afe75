import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Paper, Typography, TextField, InputAdornment } from '@mui/material';
import { Search, ExpandMore, ExpandLess } from '@mui/icons-material';
import {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions,
  zIndex
} from '../../constants/styleConstants';

const FormSelect = ({
  label,
  value,
  onChange,
  options = [],
  required = false,
  disabled = false,
  error,
  placeholder = '请选择',
  name,
  // Multiple selection
  multiple = false,
  // Search/filter options
  searchable = false,
  searchPlaceholder = '搜索选项...',
  // Style options
  fullWidth = true,
  size = 'medium',
  maxHeight = 200,
  // Callbacks
  onFocus,
  onBlur,
  // Additional props
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);

  const selectRef = useRef(null);
  const searchInputRef = useRef(null);

  // Filter options based on search term
  useEffect(() => {
    if (searchable && searchTerm) {
      const filtered = options.filter(
        option =>
          option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (option.value && option.value.toString().toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredOptions(filtered);
    } else {
      setFilteredOptions(options);
    }
  }, [searchTerm, options, searchable]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = event => {
      if (selectRef.current && !selectRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    }
  }, [isOpen, searchable]);

  const handleToggle = () => {
    if (disabled) {
      return;
    }

    setIsOpen(!isOpen);
    if (!isOpen && onFocus) {
      onFocus({ target: { name, value } });
    }
    if (isOpen && onBlur) {
      onBlur({ target: { name, value } });
    }
  };

  const handleOptionClick = optionValue => {
    if (disabled) {
      return;
    }

    let newValue;
    if (multiple) {
      const currentValues = Array.isArray(value) ? value : [];
      if (currentValues.includes(optionValue)) {
        newValue = currentValues.filter(v => v !== optionValue);
      } else {
        newValue = [...currentValues, optionValue];
      }
    } else {
      newValue = optionValue;
      setIsOpen(false);
    }

    const mockEvent = {
      target: { name, value: newValue }
    };
    onChange(mockEvent);
    setSearchTerm('');
  };

  const handleKeyDown = event => {
    if (disabled) {
      return;
    }

    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        handleToggle();
        break;
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        }
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (isOpen) {
          setIsOpen(false);
        }
        break;
      default:
        break;
    }
  };

  const getSelectedLabel = () => {
    if (multiple) {
      const selectedValues = Array.isArray(value) ? value : [];
      if (selectedValues.length === 0) {
        return placeholder;
      }
      if (selectedValues.length === 1) {
        const option = options.find(opt => opt.value === selectedValues[0]);
        return option ? option.label : placeholder;
      }
      return `已选择 ${selectedValues.length} 项`;
    } else {
      const selectedOption = options.find(opt => opt.value === value);
      return selectedOption ? selectedOption.label : placeholder;
    }
  };

  const isSelected = optionValue => {
    if (multiple) {
      return Array.isArray(value) && value.includes(optionValue);
    }
    return value === optionValue;
  };

  const getSelectStyles = () => ({
    width: fullWidth ? '100%' : 'auto',
    padding: size === 'small' ? `${spacing.xs} ${spacing.sm}` : `${spacing.sm} ${spacing.md}`,
    borderRadius: dimensions.borderRadius.base,
    border: `${dimensions.borderWidth.base} solid ${
      error ? colors.error.main : colors.border.main
    }`,
    fontSize: size === 'small' ? typography.fontSize.xs : typography.fontSize.sm,
    fontFamily: typography.fontFamily.base,
    height: size === 'small' ? '28px' : dimensions.height.input,
    boxSizing: 'border-box',
    backgroundColor: disabled ? colors.background.light : colors.background.paper,
    color: disabled ? colors.text.disabled : colors.text.primary,
    cursor: disabled ? 'not-allowed' : 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    transition: transitions.all,
    opacity: disabled ? 0.6 : 1,
    boxShadow: error ? `0 0 0 1px ${colors.error.main}` : 'none',

    '&:hover': {
      borderColor: disabled ? colors.border.main : error ? colors.error.dark : colors.border.dark
    },

    '&:focus': {
      outline: 'none',
      borderColor: error ? colors.error.main : colors.primary.main,
      boxShadow: error ? `0 0 0 1px ${colors.error.main}` : `0 0 0 1px ${colors.primary.main}`
    }
  });

  const getDropdownStyles = () => ({
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    zIndex: zIndex.dropdown,
    marginTop: '2px',
    maxHeight: `${maxHeight}px`,
    overflowY: 'auto',
    backgroundColor: colors.background.paper,
    boxShadow: shadows.dropdown,
    borderRadius: dimensions.borderRadius.base,
    border: `${dimensions.borderWidth.base} solid ${colors.border.light}`
  });

  const getOptionStyles = option => ({
    padding: `${spacing.sm} ${spacing.md}`,
    fontSize: typography.fontSize.sm,
    cursor: 'pointer',
    transition: transitions.colors,
    display: 'flex',
    alignItems: 'center',
    backgroundColor: isSelected(option.value) ? colors.primary.light : 'transparent',
    color: isSelected(option.value) ? colors.primary.contrastText : colors.text.primary,

    '&:hover': {
      backgroundColor: isSelected(option.value) ? colors.primary.main : colors.background.hover
    },

    '&:not(:last-child)': {
      borderBottom: `1px solid ${colors.border.light}`
    }
  });

  const getLabelStyles = () => ({
    display: 'block',
    marginBottom: spacing.xs,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: error ? colors.error.main : colors.text.primary,
    fontFamily: typography.fontFamily.base
  });

  return (
    <Box sx={{ marginBottom: spacing.md, position: 'relative' }} ref={selectRef}>
      <Typography variant="body2" component="label" sx={getLabelStyles()}>
        {label}
        {required && <span style={{ color: colors.error.main, marginLeft: '2px' }}>*</span>}
      </Typography>

      <Box
        onClick={handleToggle}
        onKeyDown={handleKeyDown}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-disabled={disabled}
        tabIndex={disabled ? -1 : 0}
        sx={getSelectStyles()}
        {...props}
      >
        <span style={{ flex: 1, textAlign: 'left' }}>{getSelectedLabel()}</span>
        <Box sx={{ marginLeft: spacing.sm, display: 'flex', alignItems: 'center' }}>
          {isOpen ? <ExpandLess /> : <ExpandMore />}
        </Box>
      </Box>

      {isOpen && !disabled && (
        <Paper sx={getDropdownStyles()}>
          {searchable && (
            <Box sx={{ padding: spacing.sm, borderBottom: `1px solid ${colors.border.light}` }}>
              <TextField
                ref={searchInputRef}
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                placeholder={searchPlaceholder}
                size="small"
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search fontSize="small" />
                    </InputAdornment>
                  )
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    fontSize: typography.fontSize.xs,
                    height: '28px'
                  }
                }}
              />
            </Box>
          )}

          <Box role="listbox">
            {filteredOptions.length === 0 ? (
              <Box
                sx={{
                  padding: spacing.md,
                  textAlign: 'center',
                  color: colors.text.secondary,
                  fontSize: typography.fontSize.sm
                }}
              >
                {searchTerm ? '未找到匹配项' : '暂无选项'}
              </Box>
            ) : (
              filteredOptions.map(option => (
                <Box
                  key={option.value}
                  onClick={() => handleOptionClick(option.value)}
                  sx={getOptionStyles(option)}
                  role="option"
                  aria-selected={isSelected(option.value)}
                >
                  {multiple && (
                    <Box
                      sx={{
                        marginRight: spacing.sm,
                        width: '16px',
                        height: '16px',
                        border: `1px solid ${colors.border.main}`,
                        borderRadius: '2px',
                        backgroundColor: isSelected(option.value)
                          ? colors.primary.main
                          : 'transparent',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      {isSelected(option.value) && (
                        <Box
                          sx={{
                            width: '8px',
                            height: '8px',
                            backgroundColor: colors.primary.contrastText,
                            borderRadius: '1px'
                          }}
                        />
                      )}
                    </Box>
                  )}
                  {option.label}
                </Box>
              ))
            )}
          </Box>
        </Paper>
      )}

      {error && (
        <Typography
          variant="caption"
          sx={{
            color: colors.error.main,
            display: 'block',
            marginTop: spacing.xs,
            fontSize: typography.fontSize.xs
          }}
        >
          {error}
        </Typography>
      )}
    </Box>
  );
};

FormSelect.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.array]),
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      label: PropTypes.string.isRequired
    })
  ).isRequired,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  error: PropTypes.string,
  placeholder: PropTypes.string,
  name: PropTypes.string,
  // Multiple selection
  multiple: PropTypes.bool,
  // Search options
  searchable: PropTypes.bool,
  searchPlaceholder: PropTypes.string,
  // Style options
  fullWidth: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium']),
  maxHeight: PropTypes.number,
  // Callbacks
  onFocus: PropTypes.func,
  onBlur: PropTypes.func
};

export default FormSelect;
