import { useState, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  useTheme,
  useMediaQuery,
  Box,
  Tooltip,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  CircularProgress,
  Typography,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  Checkbox,
  TableSortLabel,
  TableBody,
  TablePagination
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  FileDownload as ExportIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions
} from 'constants/styleConstants';

/**
 * Enhanced GenericDataTable Component
 *
 * A comprehensive data table component with advanced features:
 * - Sorting (client-side and server-side)
 * - Filtering and searching
 * - Row selection (single and multiple)
 * - Pagination with customizable page sizes
 * - Loading states and empty states
 * - Responsive design
 * - Export functionality
 * - Customizable column configurations
 * - Action buttons and custom renderers
 * - Consistent styling using design system
 */
const EnhancedGenericDataTable = ({
  // Data props
  data = [],
  columns = [],

  // Pagination props
  pagination = true,
  pageSize = 10,
  pageSizeOptions = [5, 10, 25, 50, 100],
  totalCount, // For server-side pagination

  // Selection props
  selectable = false,
  selected = [],
  onSelectionChange,

  // Sorting props
  sortable = true,
  sortBy,
  sortOrder = 'asc',
  onSortChange,

  // Filtering props
  searchable = true,
  searchValue = '',
  onSearchChange,
  filters = [],
  onFilterChange,

  // Loading and empty states
  loading = false,
  empty = false,
  emptyMessage = '暂无数据',
  loadingMessage = '加载中...',

  // Export props
  exportable = false,
  onExport,
  exportFileName = 'data-export',

  // Styling props
  compact = false,
  stickyHeader = false,
  showBorders = true,
  stripedRows = true,
  hoverRows = true,
  density = 'standard', // 'compact', 'standard', 'comfortable'

  // Action props
  actions = [],
  onRowClick,
  onRowDoubleClick,

  // Custom render props
  renderToolbar,
  renderEmptyState,
  renderLoadingState,
  renderRowActions,

  // Responsive props
  responsive = true,
  mobileBreakpoint = 'md',

  // Accessibility props
  ariaLabel = '数据表格',
  caption,

  // Formatting props
  formatCurrency,
  dateFormat = 'YYYY-MM-DD',

  // Advanced props
  virtualScrolling = false,
  infiniteScroll = false,
  onLoadMore,

  // Event props
  onRefresh,

  // Style overrides
  sx = {},
  className = '',

  // Legacy props for backward compatibility
  renderActions,
  actionColumnWidth = '10%',
  actionColumnTitle = '操作',
  rowHeight,
  fontSize
}) => {
  const theme = useTheme();
  useMediaQuery(theme.breakpoints.down(mobileBreakpoint));

  // Local state
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(pageSize);
  const [localSearchValue, setLocalSearchValue] = useState(searchValue);
  const [localSortBy, setLocalSortBy] = useState(sortBy);
  const [localSortOrder, setLocalSortOrder] = useState(sortOrder);
  const [localSelected, setLocalSelected] = useState(selected);

  // Default currency formatter
  const defaultFormatCurrency = useCallback(value => {
    if (value === null || value === undefined) {
      return '-';
    }
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(value);
  }, []);

  const formatFn = formatCurrency || defaultFormatCurrency;

  // Enhanced column configuration
  const enhancedColumns = useMemo(() => {
    const baseColumns = columns.map(col => ({
      field: col.field,
      headerName: col.headerName || col.field,
      width: col.width || 'auto',
      minWidth: col.minWidth || 120,
      maxWidth: col.maxWidth,
      align: col.align || (col.type === 'number' ? 'right' : 'left'),
      type: col.type || 'string',
      sortable: col.sortable !== false && sortable,
      filterable: col.filterable !== false,
      searchable: col.searchable !== false,
      format: col.format,
      render: col.render,
      headerAlign: col.headerAlign || 'center',
      cellClassName: col.cellClassName,
      headerClassName: col.headerClassName,
      ...col
    }));

    // Add action column if renderActions or actions provided
    if (renderActions || actions.length > 0) {
      baseColumns.push({
        field: '__actions__',
        headerName: actionColumnTitle,
        width: actionColumnWidth,
        align: 'center',
        sortable: false,
        filterable: false,
        searchable: false,
        isAction: true
      });
    }

    return baseColumns;
  }, [columns, sortable, renderActions, actions, actionColumnTitle, actionColumnWidth]);

  // Data processing with search and filter
  const processedData = useMemo(() => {
    let result = [...data];

    // Apply search
    if (localSearchValue && !onSearchChange) {
      const searchLower = localSearchValue.toLowerCase();
      result = result.filter(row =>
        enhancedColumns.some(col => {
          if (!col.searchable) {
            return false;
          }
          const value = row[col.field];
          return value && value.toString().toLowerCase().includes(searchLower);
        })
      );
    }

    // Apply sorting
    if (localSortBy && !onSortChange) {
      result = result.sort((a, b) => {
        const aValue = a[localSortBy];
        const bValue = b[localSortBy];

        if (aValue === null && bValue === null) {
          return 0;
        }
        if (aValue === null || aValue === undefined) {
          return localSortOrder === 'asc' ? -1 : 1;
        }
        if (bValue === null || bValue === undefined) {
          return localSortOrder === 'asc' ? 1 : -1;
        }

        let comparison = 0;
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          comparison = aValue - bValue;
        } else {
          comparison = aValue.toString().localeCompare(bValue.toString());
        }

        return localSortOrder === 'asc' ? comparison : -comparison;
      });
    }

    return result;
  }, [
    data,
    localSearchValue,
    localSortBy,
    localSortOrder,
    enhancedColumns,
    onSearchChange,
    onSortChange
  ]);

  // Pagination data
  const paginatedData = useMemo(() => {
    if (!pagination) {
      return processedData;
    }
    const start = page * rowsPerPage;
    const end = start + rowsPerPage;
    return processedData.slice(start, end);
  }, [processedData, page, rowsPerPage, pagination]);

  // Get density-based dimensions
  const getDensityDimensions = () => {
    const rowHeightOverride = rowHeight;
    const fontSizeOverride = fontSize;

    if (rowHeightOverride && fontSizeOverride) {
      return {
        rowHeight: rowHeightOverride,
        fontSize: fontSizeOverride,
        padding: compact ? spacing.xs : spacing.sm
      };
    }

    switch (density) {
      case 'compact':
        return {
          rowHeight: dimensions.height.tableRow * 0.75,
          fontSize: typography.fontSize.xs,
          padding: spacing.xs
        };
      case 'comfortable':
        return {
          rowHeight: dimensions.height.tableRow * 1.25,
          fontSize: typography.fontSize.base,
          padding: spacing.md
        };
      default:
        return {
          rowHeight: dimensions.height.tableRow,
          fontSize: typography.fontSize.sm,
          padding: spacing.sm
        };
    }
  };

  const densityDimensions = getDensityDimensions();

  // Event handlers
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = event => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = event => {
    const value = event.target.value;
    setLocalSearchValue(value);
    if (onSearchChange) {
      onSearchChange(value);
    }
  };

  const handleSortChange = field => {
    const isAsc = localSortBy === field && localSortOrder === 'asc';
    const newOrder = isAsc ? 'desc' : 'asc';
    setLocalSortBy(field);
    setLocalSortOrder(newOrder);

    if (onSortChange) {
      onSortChange(field, newOrder);
    }
  };

  const handleSelectAllClick = event => {
    if (event.target.checked) {
      const newSelected = processedData.map(row => row.id || row);
      setLocalSelected(newSelected);
      if (onSelectionChange) {
        onSelectionChange(newSelected);
      }
    } else {
      setLocalSelected([]);
      if (onSelectionChange) {
        onSelectionChange([]);
      }
    }
  };

  const handleRowSelect = (event, row) => {
    event.stopPropagation();
    const selectedIndex = localSelected.indexOf(row.id || row);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(localSelected, row.id || row);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(localSelected.slice(1));
    } else if (selectedIndex === localSelected.length - 1) {
      newSelected = newSelected.concat(localSelected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        localSelected.slice(0, selectedIndex),
        localSelected.slice(selectedIndex + 1)
      );
    }

    setLocalSelected(newSelected);
    if (onSelectionChange) {
      onSelectionChange(newSelected);
    }
  };

  const isSelected = row => {
    return localSelected.indexOf(row.id || row) !== -1;
  };

  // Render cell content
  const renderCell = (row, column) => {
    const value = row[column.field];

    // Handle action column
    if (column.isAction) {
      return (
        <Box display="flex" gap={1} justifyContent="center">
          {renderActions && renderActions(row)}
          {actions.map((action, index) => (
            <Tooltip key={index} title={action.tooltip || action.label}>
              <IconButton
                size="small"
                onClick={e => {
                  e.stopPropagation();
                  action.onClick(row);
                }}
                disabled={action.disabled && action.disabled(row)}
                color={action.color || 'default'}
              >
                {action.icon}
              </IconButton>
            </Tooltip>
          ))}
        </Box>
      );
    }

    // Custom render function
    if (column.render) {
      return column.render(value, row, column);
    }

    // Format based on type
    if (column.type === 'number') {
      return formatFn(value);
    }

    if (column.type === 'boolean') {
      return (
        <Chip label={value ? '是' : '否'} size="small" color={value ? 'success' : 'default'} />
      );
    }

    if (column.type === 'date' && value) {
      return new Date(value).toLocaleDateString('zh-CN');
    }

    // Apply custom format function
    if (column.format && typeof column.format === 'function') {
      return column.format(value, row);
    }

    return value || '-';
  };

  // Render toolbar
  const renderToolbarContent = () => {
    if (renderToolbar) {
      return renderToolbar();
    }

    return (
      <Box display="flex" alignItems="center" gap={2} mb={2}>
        {searchable && (
          <TextField
            size="small"
            placeholder="搜索..."
            value={localSearchValue}
            onChange={handleSearchChange}
            sx={{ minWidth: 200 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: localSearchValue && (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={() => {
                      setLocalSearchValue('');
                      if (onSearchChange) {
                        onSearchChange('');
                      }
                    }}
                  >
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              )
            }}
          />
        )}

        <Box flexGrow={1} />

        {exportable && (
          <Tooltip title="导出数据">
            <IconButton onClick={onExport}>
              <ExportIcon />
            </IconButton>
          </Tooltip>
        )}

        {onRefresh && (
          <Tooltip title="刷新数据">
            <IconButton onClick={onRefresh}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    );
  };

  // Render empty state
  const renderEmptyStateContent = () => {
    if (renderEmptyState) {
      return renderEmptyState();
    }

    return (
      <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" py={4}>
        <Typography variant="h6" color="text.secondary">
          {emptyMessage}
        </Typography>
      </Box>
    );
  };

  // Render loading state
  const renderLoadingStateContent = () => {
    if (renderLoadingState) {
      return renderLoadingState();
    }

    return (
      <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" py={4}>
        <CircularProgress size={40} sx={{ mb: 2 }} />
        <Typography variant="body2" color="text.secondary">
          {loadingMessage}
        </Typography>
      </Box>
    );
  };

  if (loading) {
    return (
      <Paper sx={{ width: '100%', overflow: 'hidden', ...sx }} className={className}>
        {renderToolbarContent()}
        {renderLoadingStateContent()}
      </Paper>
    );
  }

  if (empty || processedData.length === 0) {
    return (
      <Paper sx={{ width: '100%', overflow: 'hidden', ...sx }} className={className}>
        {renderToolbarContent()}
        {renderEmptyStateContent()}
      </Paper>
    );
  }

  const selectedCount = localSelected.length;
  const isAllSelected = selectedCount === processedData.length;
  const isIndeterminate = selectedCount > 0 && selectedCount < processedData.length;

  return (
    <Paper
      sx={{
        width: '100%',
        overflow: 'hidden',
        boxShadow: shadows.card,
        ...sx
      }}
      className={className}
    >
      {caption && (
        <Typography variant="h6" component="caption" sx={{ p: 2 }}>
          {caption}
        </Typography>
      )}

      {renderToolbarContent()}

      <TableContainer>
        <Table
          stickyHeader={stickyHeader}
          size={density === 'compact' ? 'small' : 'medium'}
          aria-label={ariaLabel}
          sx={{
            fontSize: densityDimensions.fontSize,
            '& .MuiTableCell-root': {
              borderBottom: showBorders ? `1px solid ${colors.border.main}` : 'none',
              fontSize: densityDimensions.fontSize,
              padding: densityDimensions.padding
            },
            '& .MuiTableRow-root': {
              height: densityDimensions.rowHeight,
              ...(stripedRows && {
                '&:nth-of-type(even)': {
                  backgroundColor: colors.background.light
                }
              }),
              ...(hoverRows && {
                '&:hover': {
                  backgroundColor: colors.background.hover,
                  transition: transitions.colors
                }
              })
            }
          }}
        >
          <TableHead>
            <TableRow>
              {selectable && (
                <TableCell padding="checkbox">
                  <Checkbox
                    color="primary"
                    indeterminate={isIndeterminate}
                    checked={isAllSelected}
                    onChange={handleSelectAllClick}
                    inputProps={{
                      'aria-label': 'select all items'
                    }}
                  />
                </TableCell>
              )}

              {enhancedColumns.map(column => (
                <TableCell
                  key={column.field}
                  align={column.headerAlign}
                  style={{
                    width: column.width,
                    minWidth: column.minWidth,
                    maxWidth: column.maxWidth
                  }}
                  className={column.headerClassName}
                  sx={{
                    fontWeight: typography.fontWeight.semibold,
                    backgroundColor: colors.background.default,
                    color: colors.text.primary
                  }}
                >
                  {column.sortable ? (
                    <TableSortLabel
                      active={localSortBy === column.field}
                      direction={localSortBy === column.field ? localSortOrder : 'asc'}
                      onClick={() => handleSortChange(column.field)}
                    >
                      {column.headerName}
                    </TableSortLabel>
                  ) : (
                    column.headerName
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>

          <TableBody>
            {paginatedData.map((row, index) => {
              const isItemSelected = isSelected(row);
              const labelId = `enhanced-table-checkbox-${index}`;

              return (
                <TableRow
                  hover={hoverRows}
                  key={row.id || index}
                  selected={isItemSelected}
                  onClick={onRowClick ? event => onRowClick(event, row) : undefined}
                  onDoubleClick={
                    onRowDoubleClick ? event => onRowDoubleClick(event, row) : undefined
                  }
                  role={selectable ? 'checkbox' : undefined}
                  aria-checked={selectable ? isItemSelected : undefined}
                  sx={{
                    cursor: onRowClick || onRowDoubleClick ? 'pointer' : 'default'
                  }}
                >
                  {selectable && (
                    <TableCell padding="checkbox">
                      <Checkbox
                        color="primary"
                        checked={isItemSelected}
                        onChange={event => handleRowSelect(event, row)}
                        inputProps={{
                          'aria-labelledby': labelId
                        }}
                      />
                    </TableCell>
                  )}

                  {enhancedColumns.map(column => (
                    <TableCell
                      key={column.field}
                      align={column.align}
                      className={column.cellClassName}
                      sx={{
                        color: colors.text.primary,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {renderCell(row, column)}
                    </TableCell>
                  ))}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {pagination && (
        <TablePagination
          rowsPerPageOptions={pageSizeOptions}
          component="div"
          count={totalCount || processedData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          labelRowsPerPage="每页显示行数:"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} 共 ${count !== -1 ? count : `超过 ${to}`} 条`
          }
          sx={{
            borderTop: `1px solid ${colors.border.main}`,
            '& .MuiTablePagination-toolbar': {
              fontSize: typography.fontSize.sm
            }
          }}
        />
      )}
    </Paper>
  );
};

// PropTypes for type checking
EnhancedGenericDataTable.propTypes = {
  // Data props
  data: PropTypes.array,
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      field: PropTypes.string.isRequired,
      headerName: PropTypes.string,
      width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      minWidth: PropTypes.number,
      maxWidth: PropTypes.number,
      align: PropTypes.oneOf(['left', 'center', 'right']),
      headerAlign: PropTypes.oneOf(['left', 'center', 'right']),
      type: PropTypes.oneOf(['string', 'number', 'boolean', 'date']),
      sortable: PropTypes.bool,
      filterable: PropTypes.bool,
      searchable: PropTypes.bool,
      format: PropTypes.func,
      render: PropTypes.func,
      cellClassName: PropTypes.string,
      headerClassName: PropTypes.string
    })
  ),

  // Pagination props
  pagination: PropTypes.bool,
  pageSize: PropTypes.number,
  pageSizeOptions: PropTypes.arrayOf(PropTypes.number),
  totalCount: PropTypes.number,

  // Selection props
  selectable: PropTypes.bool,
  selected: PropTypes.array,
  onSelectionChange: PropTypes.func,

  // Sorting props
  sortable: PropTypes.bool,
  sortBy: PropTypes.string,
  sortOrder: PropTypes.oneOf(['asc', 'desc']),
  onSortChange: PropTypes.func,

  // Filtering props
  searchable: PropTypes.bool,
  searchValue: PropTypes.string,
  onSearchChange: PropTypes.func,
  filters: PropTypes.array,
  onFilterChange: PropTypes.func,

  // Loading and empty states
  loading: PropTypes.bool,
  empty: PropTypes.bool,
  emptyMessage: PropTypes.string,
  loadingMessage: PropTypes.string,

  // Export props
  exportable: PropTypes.bool,
  onExport: PropTypes.func,
  exportFileName: PropTypes.string,

  // Styling props
  compact: PropTypes.bool,
  stickyHeader: PropTypes.bool,
  showBorders: PropTypes.bool,
  stripedRows: PropTypes.bool,
  hoverRows: PropTypes.bool,
  density: PropTypes.oneOf(['compact', 'standard', 'comfortable']),

  // Action props
  actions: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      icon: PropTypes.node,
      onClick: PropTypes.func.isRequired,
      disabled: PropTypes.func,
      color: PropTypes.string,
      tooltip: PropTypes.string
    })
  ),
  onRowClick: PropTypes.func,
  onRowDoubleClick: PropTypes.func,

  // Custom render props
  renderToolbar: PropTypes.func,
  renderEmptyState: PropTypes.func,
  renderLoadingState: PropTypes.func,
  renderRowActions: PropTypes.func,

  // Responsive props
  responsive: PropTypes.bool,
  mobileBreakpoint: PropTypes.string,

  // Accessibility props
  ariaLabel: PropTypes.string,
  caption: PropTypes.string,

  // Formatting props
  formatCurrency: PropTypes.func,
  dateFormat: PropTypes.string,

  // Advanced props
  virtualScrolling: PropTypes.bool,
  infiniteScroll: PropTypes.bool,
  onLoadMore: PropTypes.func,

  // Event props
  onRefresh: PropTypes.func,

  // Style overrides
  sx: PropTypes.object,
  className: PropTypes.string,

  // Legacy props for backward compatibility
  renderActions: PropTypes.func,
  actionColumnWidth: PropTypes.string,
  actionColumnTitle: PropTypes.string,
  rowHeight: PropTypes.number,
  fontSize: PropTypes.number
};

export default EnhancedGenericDataTable;
