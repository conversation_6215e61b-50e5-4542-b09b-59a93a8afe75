/**
 * Table Components Index
 *
 * This file exports all table-related components used throughout the FinancialSystem application.
 * It provides a centralized location for importing table components and ensures consistent usage.
 */

// Core table components
export { default as GenericDataTable } from './GenericDataTable';
export { default as ImprovedGenericDataTable } from './ImprovedGenericDataTable';
export { default as EnhancedGenericDataTable } from './EnhancedGenericDataTable';

// Table component aliases for convenience
export { default as DataTable } from './GenericDataTable';
export { default as Table } from './ImprovedGenericDataTable';
export { default as AdvancedTable } from './EnhancedGenericDataTable';

// Re-export the debt management PaginatedTable for compatibility
export { default as PaginatedTable } from '../../layouts/debtmanagement/components/PagingatedTable';

/**
 * Component Usage Guide:
 *
 * 1. GenericDataTable (Original)
 *    - Basic table with pagination
 *    - Minimal styling
 *    - Backward compatible
 *    - Use for: Legacy code, simple tables
 *
 * 2. ImprovedGenericDataTable (Recommended)
 *    - Enhanced styling with design system
 *    - Backward compatible with GenericDataTable
 *    - Better performance and accessibility
 *    - Use for: General purpose tables, migration from GenericDataTable
 *
 * 3. EnhancedGenericDataTable (Advanced)
 *    - Full-featured table with sorting, filtering, selection
 *    - Material-UI integration
 *    - Advanced features like export, loading states
 *    - Use for: Complex data tables, admin interfaces
 *
 * 4. PaginatedTable (Debt Management)
 *    - Specialized for debt management module
 *    - Fixed column widths and sorting
 *    - Use for: Debt management related tables
 *
 * Examples:
 *
 * // Basic usage (backward compatible)
 * import { GenericDataTable } from 'components/tables';
 *
 * // Improved styling (recommended)
 * import { ImprovedGenericDataTable as DataTable } from 'components/tables';
 *
 * // Advanced features
 * import { EnhancedGenericDataTable as AdvancedTable } from 'components/tables';
 *
 * // Debt management specific
 * import { PaginatedTable } from 'components/tables';
 */

// Table utility functions and constants
export const TABLE_DENSITIES = {
  COMPACT: 'compact',
  STANDARD: 'standard',
  COMFORTABLE: 'comfortable'
};

export const TABLE_SORT_ORDERS = {
  ASC: 'asc',
  DESC: 'desc'
};

export const TABLE_COLUMN_TYPES = {
  STRING: 'string',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  DATE: 'date',
  CURRENCY: 'currency',
  PERCENT: 'percent'
};

export const TABLE_ALIGNMENT = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right'
};

// Common table configuration helpers
export const createTableColumn = ({
  field,
  headerName,
  width = 'auto',
  type = TABLE_COLUMN_TYPES.STRING,
  align = TABLE_ALIGNMENT.LEFT,
  sortable = true,
  filterable = true,
  searchable = true,
  render = null,
  format = null,
  ...rest
}) => ({
  field,
  headerName: headerName || field,
  width,
  type,
  align: type === TABLE_COLUMN_TYPES.NUMBER ? TABLE_ALIGNMENT.RIGHT : align,
  sortable,
  filterable,
  searchable,
  render,
  format,
  ...rest
});

export const createCurrencyColumn = (field, headerName, width = '120px') =>
  createTableColumn({
    field,
    headerName,
    width,
    type: TABLE_COLUMN_TYPES.NUMBER,
    align: TABLE_ALIGNMENT.RIGHT
  });

export const createDateColumn = (field, headerName, width = '120px') =>
  createTableColumn({
    field,
    headerName,
    width,
    type: TABLE_COLUMN_TYPES.DATE,
    align: TABLE_ALIGNMENT.CENTER
  });

export const createBooleanColumn = (field, headerName, width = '80px') =>
  createTableColumn({
    field,
    headerName,
    width,
    type: TABLE_COLUMN_TYPES.BOOLEAN,
    align: TABLE_ALIGNMENT.CENTER
  });

export const createActionColumn = (actions = [], width = '100px', headerName = '操作') => ({
  field: '__actions__',
  headerName,
  width,
  align: TABLE_ALIGNMENT.CENTER,
  sortable: false,
  filterable: false,
  searchable: false,
  render: (value, row) => (
    <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
      {actions.map((action, index) => (
        <button
          key={index}
          onClick={e => {
            e.stopPropagation();
            action.onClick(row);
          }}
          disabled={action.disabled && action.disabled(row)}
          style={{
            padding: '4px 8px',
            border: '1px solid #d0d0d0',
            borderRadius: '4px',
            backgroundColor: '#f5f5f5',
            cursor: 'pointer',
            fontSize: '12px'
          }}
          title={action.tooltip}
        >
          {action.label}
        </button>
      ))}
    </div>
  )
});

// Common table configurations for different use cases
export const TABLE_CONFIGURATIONS = {
  // Basic configuration
  BASIC: {
    pagination: true,
    pageSize: 10,
    sortable: true,
    searchable: true,
    density: TABLE_DENSITIES.STANDARD,
    showBorders: true,
    hoverRows: true,
    stripedRows: false
  },

  // Compact configuration for mobile or limited space
  COMPACT: {
    pagination: true,
    pageSize: 5,
    sortable: true,
    searchable: true,
    density: TABLE_DENSITIES.COMPACT,
    showBorders: true,
    hoverRows: true,
    stripedRows: true
  },

  // Full-featured configuration
  ADVANCED: {
    pagination: true,
    pageSize: 25,
    pageSizeOptions: [10, 25, 50, 100],
    sortable: true,
    searchable: true,
    selectable: true,
    exportable: true,
    density: TABLE_DENSITIES.STANDARD,
    showBorders: true,
    hoverRows: true,
    stripedRows: false,
    stickyHeader: true
  },

  // Read-only configuration
  READONLY: {
    pagination: true,
    pageSize: 20,
    sortable: false,
    searchable: false,
    selectable: false,
    exportable: false,
    density: TABLE_DENSITIES.STANDARD,
    showBorders: true,
    hoverRows: false,
    stripedRows: true
  }
};

// Export default configuration for quick setup
export const DEFAULT_TABLE_CONFIG = TABLE_CONFIGURATIONS.BASIC;

// Migration helper for converting old GenericDataTable props to new format
export const migrateTableProps = oldProps => {
  const {
    columns = [],
    data = [],
    formatCurrency,
    pageSize = 10,
    renderActions,
    actionColumnWidth = '10%',
    actionColumnTitle = '操作',
    compact = false,
    rowHeight,
    fontSize,
    ...rest
  } = oldProps;

  // Convert columns to new format
  const enhancedColumns = columns.map(col => createTableColumn(col));

  // Add action column if needed
  if (renderActions) {
    enhancedColumns.push({
      field: '__actions__',
      headerName: actionColumnTitle,
      width: actionColumnWidth,
      align: TABLE_ALIGNMENT.CENTER,
      sortable: false,
      filterable: false,
      searchable: false,
      render: (value, row) => renderActions(row)
    });
  }

  return {
    columns: enhancedColumns,
    data,
    formatCurrency,
    pageSize,
    density: compact ? TABLE_DENSITIES.COMPACT : TABLE_DENSITIES.STANDARD,
    ...(rowHeight && { rowHeight }),
    ...(fontSize && { fontSize }),
    ...rest
  };
};

// Import all components for default export
import GenericDataTableComponent from './GenericDataTable';
import ImprovedGenericDataTableComponent from './ImprovedGenericDataTable';
import EnhancedGenericDataTableComponent from './EnhancedGenericDataTable';
import PaginatedTableComponent from '../../layouts/debtmanagement/components/PagingatedTable';

// Export everything as default for convenience
export default {
  GenericDataTable: GenericDataTableComponent,
  ImprovedGenericDataTable: ImprovedGenericDataTableComponent,
  EnhancedGenericDataTable: EnhancedGenericDataTableComponent,
  PaginatedTable: PaginatedTableComponent,
  TABLE_DENSITIES,
  TABLE_SORT_ORDERS,
  TABLE_COLUMN_TYPES,
  TABLE_ALIGNMENT,
  TABLE_CONFIGURATIONS,
  DEFAULT_TABLE_CONFIG,
  createTableColumn,
  createCurrencyColumn,
  createDateColumn,
  createBooleanColumn,
  createActionColumn,
  migrateTableProps
};
