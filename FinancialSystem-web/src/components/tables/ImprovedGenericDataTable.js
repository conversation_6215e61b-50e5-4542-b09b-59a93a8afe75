import React, { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions
} from 'constants/styleConstants';

/**
 * Improved GenericDataTable Component
 *
 * An enhanced version of the original GenericDataTable with improved styling
 * using the unified style constants while maintaining full backward compatibility.
 *
 * Features:
 * - Uses unified style constants for consistent theming
 * - Improved responsive design
 * - Better accessibility
 * - Enhanced hover and selection states
 * - Optimized performance
 * - Loading and empty states
 * - Better mobile support
 *
 * @param {Object} props - Component props (same as original GenericDataTable)
 */
const ImprovedGenericDataTable = ({
  columns,
  data,
  formatCurrency,
  pageSize = 10,
  renderActions,
  actionColumnWidth = '10%',
  actionColumnTitle = '操作',
  compact = false,
  rowHeight = 40,
  fontSize = 13,
  loading = false,
  empty = false,
  emptyMessage = '暂无数据',
  loadingMessage = '加载中...',
  onRowClick,
  onRowDoubleClick,
  stickyHeader = false,
  showBorders = true,
  stripedRows = false,
  hoverRows = true,
  // responsive = true,
  className = '',
  style = {},
  ariaLabel = '数据表格',
  caption,
  ...restProps
}) => {
  // Internal state
  const [currentPage, setCurrentPage] = useState(0);
  const [isLoading] = useState(loading);

  // Default currency formatter with improved formatting
  const defaultFormatCurrency = value => {
    if (value === null || value === undefined || value === '') {
      return '-';
    }
    if (typeof value !== 'number') {
      return value;
    }

    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatFn = formatCurrency || defaultFormatCurrency;

  // Enhanced data processing
  const processedData = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    // Add row indices for better key management
    return data.map((item, index) => ({
      ...item,
      __rowIndex: index,
      __rowId: item.id || `row-${index}`
    }));
  }, [data]);

  // Pagination calculations
  const totalPages = Math.ceil(processedData.length / pageSize);
  const pagedData = processedData.slice(currentPage * pageSize, (currentPage + 1) * pageSize);

  // Style calculations based on design system
  const getRowHeight = () => {
    if (rowHeight && rowHeight !== 40) {
      return rowHeight;
    }
    return compact ? dimensions.height.tableRow * 0.8 : dimensions.height.tableRow;
  };

  const getFontSize = () => {
    if (fontSize && fontSize !== 13) {
      return `${fontSize}px`;
    }
    return compact ? typography.fontSize.xs : typography.fontSize.sm;
  };

  const getCellPadding = () => {
    return compact ? spacing.xs : spacing.sm;
  };

  const actualRowHeight = getRowHeight();
  const actualFontSize = getFontSize();
  const cellPadding = getCellPadding();

  // Enhanced styles using design system
  const tableStyles = {
    container: {
      width: '100%',
      overflowX: 'auto',
      fontSize: actualFontSize,
      fontFamily: typography.fontFamily.base,
      backgroundColor: colors.background.paper,
      borderRadius: dimensions.borderRadius.base,
      boxShadow: shadows.card,
      transition: transitions.shadow,
      position: 'relative',
      ...style
    },

    table: {
      width: '100%',
      borderCollapse: 'collapse',
      tableLayout: 'fixed',
      backgroundColor: colors.background.paper,
      fontSize: actualFontSize
    },

    thead: {
      backgroundColor: colors.background.default,
      position: stickyHeader ? 'sticky' : 'static',
      top: 0,
      zIndex: 1
    },

    headerCell: {
      padding: `${cellPadding} ${spacing.xs}`,
      textAlign: 'center',
      borderBottom: showBorders
        ? `${dimensions.borderWidth.thick} solid ${colors.border.main}`
        : 'none',
      fontWeight: typography.fontWeight.semibold,
      fontSize: actualFontSize,
      color: colors.text.primary,
      backgroundColor: colors.background.default,
      height: `${actualRowHeight}px`,
      verticalAlign: 'middle',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
      position: 'relative',
      userSelect: 'none'
    },

    bodyRow: {
      height: `${actualRowHeight}px`,
      transition: transitions.colors,
      cursor: onRowClick || onRowDoubleClick ? 'pointer' : 'default',
      borderBottom: showBorders
        ? `${dimensions.borderWidth.thin} solid ${colors.border.light}`
        : 'none'
    },

    bodyRowHover: {
      backgroundColor: hoverRows ? colors.background.hover : 'transparent'
    },

    bodyRowStriped: {
      backgroundColor: stripedRows ? colors.background.light : 'transparent'
    },

    bodyCell: {
      padding: `${cellPadding} ${spacing.xs}`,
      verticalAlign: 'middle',
      fontSize: actualFontSize,
      color: colors.text.primary,
      height: `${actualRowHeight}px`,
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
      borderBottom: showBorders
        ? `${dimensions.borderWidth.thin} solid ${colors.border.light}`
        : 'none'
    },

    pagination: {
      marginTop: spacing.sm,
      textAlign: 'center',
      fontSize: typography.fontSize.sm,
      padding: spacing.sm,
      borderTop: showBorders
        ? `${dimensions.borderWidth.thin} solid ${colors.border.light}`
        : 'none',
      backgroundColor: colors.background.default
    },

    paginationButton: {
      padding: `${spacing.xs} ${spacing.sm}`,
      margin: `0 ${spacing.xs}`,
      border: `${dimensions.borderWidth.thin} solid ${colors.border.main}`,
      borderRadius: dimensions.borderRadius.base,
      backgroundColor: colors.background.paper,
      color: colors.text.primary,
      fontSize: typography.fontSize.sm,
      transition: transitions.all,
      cursor: 'pointer',
      userSelect: 'none'
    },

    paginationButtonDisabled: {
      cursor: 'not-allowed',
      opacity: 0.6,
      color: colors.text.disabled
    },

    paginationButtonHover: {
      backgroundColor: colors.background.hover,
      borderColor: colors.border.dark
    },

    loadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column',
      zIndex: 2
    },

    emptyState: {
      textAlign: 'center',
      padding: spacing['3xl'],
      color: colors.text.secondary,
      fontSize: typography.fontSize.base
    },

    loadingSpinner: {
      width: '32px',
      height: '32px',
      border: `3px solid ${colors.grey[200]}`,
      borderTop: `3px solid ${colors.primary.main}`,
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginBottom: spacing.sm
    }
  };

  // Add CSS animation for loading spinner
  const spinnerKeyframes = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;

  // Event handlers
  const handlePageChange = newPage => {
    if (newPage >= 0 && newPage < totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleRowClick = (event, row) => {
    if (onRowClick) {
      onRowClick(event, row);
    }
  };

  const handleRowDoubleClick = (event, row) => {
    if (onRowDoubleClick) {
      onRowDoubleClick(event, row);
    }
  };

  // Enhanced cell rendering with better formatting
  const renderCellContent = (row, column, value) => {
    // Handle React elements
    if (React.isValidElement(value)) {
      return value;
    }

    // Handle custom render function
    if (column.render && typeof column.render === 'function') {
      return column.render(value, row, column);
    }

    // Format based on column type
    if (column.type === 'number') {
      return formatFn(value);
    }

    if (column.type === 'boolean') {
      return value ? '是' : '否';
    }

    if (column.type === 'date' && value) {
      return new Date(value).toLocaleDateString('zh-CN');
    }

    // Handle null/undefined values
    if (value === null || value === undefined || value === '') {
      return '-';
    }

    return value;
  };

  // Render loading state
  if (loading || isLoading) {
    return (
      <div style={tableStyles.container} className={className} {...restProps}>
        <style>{spinnerKeyframes}</style>
        <div style={tableStyles.loadingOverlay}>
          <div style={tableStyles.loadingSpinner}></div>
          <div>{loadingMessage}</div>
        </div>
      </div>
    );
  }

  // Render empty state
  if (empty || processedData.length === 0) {
    return (
      <div style={tableStyles.container} className={className} {...restProps}>
        <div style={tableStyles.emptyState}>{emptyMessage}</div>
      </div>
    );
  }

  return (
    <div style={tableStyles.container} className={className} {...restProps}>
      <style>{spinnerKeyframes}</style>

      {caption && (
        <div
          style={{
            padding: spacing.md,
            fontSize: typography.fontSize.lg,
            fontWeight: typography.fontWeight.medium,
            color: colors.text.primary,
            borderBottom: `${dimensions.borderWidth.thin} solid ${colors.border.light}`
          }}
        >
          {caption}
        </div>
      )}

      <table style={tableStyles.table} aria-label={ariaLabel}>
        <thead style={tableStyles.thead}>
          <tr>
            {columns.map((col, index) => (
              <th
                key={`header-${col.field || index}`}
                style={{
                  ...tableStyles.headerCell,
                  width: col.width || 'auto',
                  minWidth: col.minWidth || 'auto',
                  maxWidth: col.maxWidth || 'auto',
                  textAlign: col.headerAlign || 'center'
                }}
                title={col.headerName}
              >
                {col.headerName}
              </th>
            ))}
            {renderActions && (
              <th
                style={{
                  ...tableStyles.headerCell,
                  width: actionColumnWidth
                }}
                title={actionColumnTitle}
              >
                {actionColumnTitle}
              </th>
            )}
          </tr>
        </thead>

        <tbody>
          {pagedData.map((row, rowIdx) => {
            const isEven = rowIdx % 2 === 0;
            const rowStyle = {
              ...tableStyles.bodyRow,
              ...(stripedRows && isEven ? tableStyles.bodyRowStriped : {})
            };

            return (
              <tr
                key={row.__rowId}
                style={rowStyle}
                onClick={event => handleRowClick(event, row)}
                onDoubleClick={event => handleRowDoubleClick(event, row)}
                onMouseEnter={e => {
                  if (hoverRows) {
                    e.currentTarget.style.backgroundColor = colors.background.hover;
                  }
                }}
                onMouseLeave={e => {
                  if (hoverRows) {
                    e.currentTarget.style.backgroundColor =
                      stripedRows && isEven ? colors.background.light : 'transparent';
                  }
                }}
              >
                {columns.map((col, colIdx) => {
                  const value = row[col.field];
                  const cellAlign = col.align || (col.type === 'number' ? 'right' : 'left');

                  return (
                    <td
                      key={`cell-${row.__rowIndex}-${col.field || colIdx}`}
                      style={{
                        ...tableStyles.bodyCell,
                        textAlign: cellAlign
                      }}
                      title={typeof value === 'string' ? value : undefined}
                    >
                      {renderCellContent(row, col, value)}
                    </td>
                  );
                })}

                {renderActions && (
                  <td
                    style={{
                      ...tableStyles.bodyCell,
                      textAlign: 'center'
                    }}
                  >
                    {renderActions(row)}
                  </td>
                )}
              </tr>
            );
          })}
        </tbody>
      </table>

      {/* Enhanced pagination controls */}
      {totalPages > 1 && (
        <div style={tableStyles.pagination}>
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 0}
            style={{
              ...tableStyles.paginationButton,
              ...(currentPage === 0 ? tableStyles.paginationButtonDisabled : {})
            }}
            onMouseEnter={e => {
              if (currentPage > 0) {
                Object.assign(e.target.style, tableStyles.paginationButtonHover);
              }
            }}
            onMouseLeave={e => {
              if (currentPage > 0) {
                e.target.style.backgroundColor = colors.background.paper;
                e.target.style.borderColor = colors.border.main;
              }
            }}
          >
            上一页
          </button>

          <span
            style={{
              margin: `0 ${spacing.md}`,
              fontSize: typography.fontSize.sm,
              color: colors.text.secondary
            }}
          >
            第 {currentPage + 1} 页 / 共 {totalPages} 页
            <span style={{ marginLeft: spacing.sm, color: colors.text.hint }}>
              (共 {processedData.length} 条记录)
            </span>
          </span>

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage >= totalPages - 1}
            style={{
              ...tableStyles.paginationButton,
              ...(currentPage >= totalPages - 1 ? tableStyles.paginationButtonDisabled : {})
            }}
            onMouseEnter={e => {
              if (currentPage < totalPages - 1) {
                Object.assign(e.target.style, tableStyles.paginationButtonHover);
              }
            }}
            onMouseLeave={e => {
              if (currentPage < totalPages - 1) {
                e.target.style.backgroundColor = colors.background.paper;
                e.target.style.borderColor = colors.border.main;
              }
            }}
          >
            下一页
          </button>
        </div>
      )}
    </div>
  );
};

// PropTypes - same as original but with additional props
ImprovedGenericDataTable.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      field: PropTypes.string.isRequired,
      headerName: PropTypes.string.isRequired,
      width: PropTypes.string,
      minWidth: PropTypes.string,
      maxWidth: PropTypes.string,
      align: PropTypes.oneOf(['left', 'center', 'right']),
      headerAlign: PropTypes.oneOf(['left', 'center', 'right']),
      type: PropTypes.oneOf(['string', 'number', 'boolean', 'date']),
      render: PropTypes.func
    })
  ).isRequired,
  data: PropTypes.array.isRequired,
  formatCurrency: PropTypes.func,
  pageSize: PropTypes.number,
  renderActions: PropTypes.func,
  actionColumnWidth: PropTypes.string,
  actionColumnTitle: PropTypes.string,
  compact: PropTypes.bool,
  rowHeight: PropTypes.number,
  fontSize: PropTypes.number,

  // Additional props
  loading: PropTypes.bool,
  empty: PropTypes.bool,
  emptyMessage: PropTypes.string,
  loadingMessage: PropTypes.string,
  onRowClick: PropTypes.func,
  onRowDoubleClick: PropTypes.func,
  stickyHeader: PropTypes.bool,
  showBorders: PropTypes.bool,
  stripedRows: PropTypes.bool,
  hoverRows: PropTypes.bool,
  responsive: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
  ariaLabel: PropTypes.string,
  caption: PropTypes.string
};

export default ImprovedGenericDataTable;
