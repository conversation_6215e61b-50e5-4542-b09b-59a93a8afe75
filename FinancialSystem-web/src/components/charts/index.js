/**
 * 标准化图表组件导出文件
 *
 * 该文件导出所有标准化的图表组件，这些组件基于 Chart.js 和 Material Dashboard 2 React 的设计规范
 *
 * 组件特点：
 * - 使用统一的颜色主题和字体设计
 * - 支持响应式设计和自定义配置
 * - 包含数据标签、图例、网格等标准化功能
 * - 与项目的 Material-UI 主题系统集成
 *
 * 使用示例：
 * import { StandardBarChart, StandardLineChart, StandardPieChart, StandardMixedChart } from 'components/charts';
 */

// 导出所有标准化图表组件
export { default as StandardBarChart } from './StandardBarChart';
export { default as StandardLineChart } from './StandardLineChart';
export { default as StandardPieChart } from './StandardPieChart';
export { default as StandardMixedChart } from './StandardMixedChart';

// 可选：提供一个包含所有组件的对象导出
export default {
  StandardBarChart: require('./StandardBarChart').default,
  StandardLineChart: require('./StandardLineChart').default,
  StandardPieChart: require('./StandardPieChart').default,
  StandardMixedChart: require('./StandardMixedChart').default
};
