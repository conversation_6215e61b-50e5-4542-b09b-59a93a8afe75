/**
 * ConfirmationDialog Component
 *
 * A flexible confirmation dialog with Material-UI Dialog
 * Supports different dialog types (delete, save, warning)
 * Has customizable actions and content
 * Uses the unified style constants for buttons and layout
 */

import PropTypes from 'prop-types';
import { Dialog, DialogContent, DialogActions, Typography, Button, Box } from '@mui/material';
import {
  Close as CloseIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Help as HelpIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Import unified style constants
import { colors, spacing, shadows, typography, transitions } from 'constants/styleConstants';

// Styled components
const StyledDialog = styled(Dialog)(() => ({
  '& .MuiDialog-paper': {
    borderRadius: '8px',
    boxShadow: shadows.modal,
    minWidth: '400px',
    maxWidth: '600px'
  }
}));

const DialogHeader = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: spacing.lg,
  borderBottom: `1px solid ${colors.border.light}`
}));

const DialogTitleContent = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: spacing.sm
}));

const DialogIcon = styled(Box)(({ dialogType }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '40px',
  height: '40px',
  borderRadius: '50%',

  ...(dialogType === 'delete' && {
    backgroundColor: colors.error.light,
    color: colors.error.main
  }),

  ...(dialogType === 'save' && {
    backgroundColor: colors.success.light,
    color: colors.success.main
  }),

  ...(dialogType === 'warning' && {
    backgroundColor: colors.warning.light,
    color: colors.warning.main
  }),

  ...(dialogType === 'info' && {
    backgroundColor: colors.info.light,
    color: colors.info.main
  }),

  ...(dialogType === 'confirm' && {
    backgroundColor: colors.primary.light,
    color: colors.primary.main
  })
}));

const StyledDialogTitle = styled(Typography)(() => ({
  fontSize: typography.fontSize.lg,
  fontWeight: typography.fontWeight.semibold,
  color: colors.text.primary,
  margin: 0
}));

const StyledDialogContent = styled(DialogContent)(() => ({
  padding: spacing.lg,
  paddingTop: spacing.md
}));

const DialogDescription = styled(Typography)(() => ({
  fontSize: typography.fontSize.base,
  color: colors.text.secondary,
  lineHeight: typography.lineHeight.normal,
  marginBottom: spacing.md
}));

const StyledDialogActions = styled(DialogActions)(() => ({
  padding: spacing.lg,
  borderTop: `1px solid ${colors.border.light}`,
  gap: spacing.sm
}));

const ActionButton = styled(Button)(({ variant }) => ({
  minWidth: '80px',
  height: '36px',
  textTransform: 'none',
  fontSize: typography.fontSize.sm,
  fontWeight: typography.fontWeight.medium,
  transition: transitions.all,

  ...(variant === 'primary' && {
    backgroundColor: colors.primary.main,
    color: colors.primary.contrastText,
    '&:hover': {
      backgroundColor: colors.primary.dark
    }
  }),

  ...(variant === 'danger' && {
    backgroundColor: colors.error.main,
    color: colors.error.contrastText,
    '&:hover': {
      backgroundColor: colors.error.dark
    }
  }),

  ...(variant === 'success' && {
    backgroundColor: colors.success.main,
    color: colors.success.contrastText,
    '&:hover': {
      backgroundColor: colors.success.dark
    }
  }),

  ...(variant === 'secondary' && {
    backgroundColor: 'transparent',
    color: colors.text.secondary,
    border: `1px solid ${colors.border.main}`,
    '&:hover': {
      backgroundColor: colors.background.hover
    }
  })
}));

// Dialog type configurations
const DIALOG_CONFIGS = {
  delete: {
    icon: DeleteIcon,
    defaultTitle: '确认删除',
    defaultMessage: '确定要删除此项吗？此操作不可撤销。',
    primaryAction: { label: '删除', variant: 'danger' },
    secondaryAction: { label: '取消', variant: 'secondary' }
  },
  save: {
    icon: SaveIcon,
    defaultTitle: '确认保存',
    defaultMessage: '确定要保存这些更改吗？',
    primaryAction: { label: '保存', variant: 'success' },
    secondaryAction: { label: '取消', variant: 'secondary' }
  },
  warning: {
    icon: WarningIcon,
    defaultTitle: '警告',
    defaultMessage: '此操作可能会产生不可预期的结果，确定要继续吗？',
    primaryAction: { label: '继续', variant: 'primary' },
    secondaryAction: { label: '取消', variant: 'secondary' }
  },
  info: {
    icon: InfoIcon,
    defaultTitle: '提示',
    defaultMessage: '确定要执行此操作吗？',
    primaryAction: { label: '确定', variant: 'primary' },
    secondaryAction: { label: '取消', variant: 'secondary' }
  },
  confirm: {
    icon: HelpIcon,
    defaultTitle: '确认操作',
    defaultMessage: '确定要执行此操作吗？',
    primaryAction: { label: '确定', variant: 'primary' },
    secondaryAction: { label: '取消', variant: 'secondary' }
  }
};

const ConfirmationDialog = ({
  open = false,
  type = 'confirm',
  title = '',
  message = '',
  content = null,
  onConfirm = null,
  onCancel = null,
  onClose = null,
  confirmText = '',
  cancelText = '',
  confirmVariant = '',
  cancelVariant = '',
  showCloseIcon = true,
  disableEscapeKeyDown = false,
  disableBackdropClick = false,
  maxWidth = 'sm',
  className = '',
  ...props
}) => {
  const config = DIALOG_CONFIGS[type] || DIALOG_CONFIGS.confirm;
  const IconComponent = config.icon;

  const dialogTitle = title || config.defaultTitle;
  const dialogMessage = message || config.defaultMessage;

  const primaryAction = {
    label: confirmText || config.primaryAction.label,
    variant: confirmVariant || config.primaryAction.variant
  };

  const secondaryAction = {
    label: cancelText || config.secondaryAction.label,
    variant: cancelVariant || config.secondaryAction.variant
  };

  const handleClose = (event, reason) => {
    if (disableBackdropClick && reason === 'backdropClick') {
      return;
    }
    if (disableEscapeKeyDown && reason === 'escapeKeyDown') {
      return;
    }

    if (onClose) {
      onClose();
    } else if (onCancel) {
      onCancel();
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <StyledDialog
      open={open}
      onClose={handleClose}
      maxWidth={maxWidth}
      fullWidth
      className={className}
      {...props}
    >
      <DialogHeader>
        <DialogTitleContent>
          <DialogIcon dialogType={type}>
            <IconComponent fontSize="medium" />
          </DialogIcon>
          <StyledDialogTitle>{dialogTitle}</StyledDialogTitle>
        </DialogTitleContent>

        {showCloseIcon && (
          <IconButton
            size="small"
            onClick={handleClose}
            sx={{
              color: colors.text.secondary,
              '&:hover': { color: colors.text.primary }
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        )}
      </DialogHeader>

      <StyledDialogContent>
        {content ? content : <DialogDescription>{dialogMessage}</DialogDescription>}
      </StyledDialogContent>

      <StyledDialogActions>
        <ActionButton variant={secondaryAction.variant} onClick={handleCancel}>
          {secondaryAction.label}
        </ActionButton>

        <ActionButton variant={primaryAction.variant} onClick={handleConfirm}>
          {primaryAction.label}
        </ActionButton>
      </StyledDialogActions>
    </StyledDialog>
  );
};

ConfirmationDialog.propTypes = {
  /**
   * Whether the dialog is open
   */
  open: PropTypes.bool,

  /**
   * Type of dialog (delete, save, warning, info, confirm)
   */
  type: PropTypes.oneOf(['delete', 'save', 'warning', 'info', 'confirm']),

  /**
   * Dialog title (optional, uses default based on type)
   */
  title: PropTypes.string,

  /**
   * Dialog message (optional, uses default based on type)
   */
  message: PropTypes.string,

  /**
   * Custom content to display instead of message
   */
  content: PropTypes.node,

  /**
   * Callback when user confirms
   */
  onConfirm: PropTypes.func,

  /**
   * Callback when user cancels
   */
  onCancel: PropTypes.func,

  /**
   * Callback when dialog closes
   */
  onClose: PropTypes.func,

  /**
   * Custom text for confirm button
   */
  confirmText: PropTypes.string,

  /**
   * Custom text for cancel button
   */
  cancelText: PropTypes.string,

  /**
   * Custom variant for confirm button
   */
  confirmVariant: PropTypes.oneOf(['primary', 'danger', 'success', 'secondary']),

  /**
   * Custom variant for cancel button
   */
  cancelVariant: PropTypes.oneOf(['primary', 'danger', 'success', 'secondary']),

  /**
   * Whether to show close icon
   */
  showCloseIcon: PropTypes.bool,

  /**
   * Whether to disable escape key to close
   */
  disableEscapeKeyDown: PropTypes.bool,

  /**
   * Whether to disable backdrop click to close
   */
  disableBackdropClick: PropTypes.bool,

  /**
   * Maximum width of the dialog
   */
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),

  /**
   * Additional CSS class name
   */
  className: PropTypes.string
};

ConfirmationDialog.defaultProps = {
  open: false,
  type: 'confirm',
  title: '',
  message: '',
  content: null,
  onConfirm: null,
  onCancel: null,
  onClose: null,
  confirmText: '',
  cancelText: '',
  confirmVariant: '',
  cancelVariant: '',
  showCloseIcon: true,
  disableEscapeKeyDown: false,
  disableBackdropClick: false,
  maxWidth: 'sm',
  className: ''
};

export default ConfirmationDialog;
