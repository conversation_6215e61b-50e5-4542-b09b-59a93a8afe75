/**
 * NotificationManager - 统一弹窗管理系统
 *
 * 功能特性：
 * 1. 所有弹窗居中显示
 * 2. 按行业标准自动消失
 * 3. 支持多种弹窗类型
 * 4. 统一的样式和动画
 * 5. 可配置的显示时长
 */

import React, { createContext, useContext, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  Slide,
  Fade
} from '@mui/material';
import {
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// 行业标准的自动消失时长配置
const AUTO_HIDE_DURATIONS = {
  success: 3000, // 成功消息 - 3秒
  info: 4000, // 信息消息 - 4秒
  warning: 5000, // 警告消息 - 5秒
  error: 6000, // 错误消息 - 6秒
  confirm: 0, // 确认对话框 - 不自动消失
  toast: 2000 // 简单提示 - 2秒
};

// 居中显示的Snackbar样式
const CenteredSnackbar = styled(Snackbar)(({ theme }) => ({
  '& .MuiSnackbar-root': {
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: theme.zIndex.snackbar
  },
  '& .MuiSnackbar-anchorOriginTopCenter': {
    top: '20%',
    left: '50%',
    transform: 'translateX(-50%)'
  }
}));

// 居中显示的Dialog样式
const CenteredDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-container': {
    alignItems: 'center',
    justifyContent: 'center'
  },
  '& .MuiDialog-paper': {
    margin: 0,
    borderRadius: '12px',
    boxShadow: theme.shadows[24],
    minWidth: '320px',
    maxWidth: '600px'
  }
}));

// Toast样式组件
const ToastContainer = styled(Box)(({ theme, severity }) => ({
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  zIndex: theme.zIndex.snackbar + 1,
  minWidth: '300px',
  maxWidth: '500px',
  padding: theme.spacing(2, 3),
  borderRadius: '8px',
  boxShadow: theme.shadows[8],
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1.5),
  backgroundColor:
    severity === 'success'
      ? theme.palette.success.main
      : severity === 'error'
      ? theme.palette.error.main
      : severity === 'warning'
      ? theme.palette.warning.main
      : severity === 'info'
      ? theme.palette.info.main
      : theme.palette.grey[800],
  color: theme.palette.common.white,
  animation: 'slideInFromTop 0.3s ease-out',
  '@keyframes slideInFromTop': {
    from: {
      opacity: 0,
      transform: 'translate(-50%, -60%) scale(0.9)'
    },
    to: {
      opacity: 1,
      transform: 'translate(-50%, -50%) scale(1)'
    }
  }
}));

// 通知上下文
const NotificationContext = createContext();

// 通知Provider组件
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [dialogs, setDialogs] = useState([]);
  const [toasts, setToasts] = useState([]);

  // 生成唯一ID
  const generateId = useCallback(() => {
    return Date.now() + Math.random().toString(36).substr(2, 9);
  }, []);

  // 显示通知
  const showNotification = useCallback(
    (type, message, options = {}) => {
      const id = generateId();
      const duration = options.duration || AUTO_HIDE_DURATIONS[type] || AUTO_HIDE_DURATIONS.info;

      const notification = {
        id,
        type,
        message,
        title: options.title,
        open: true,
        duration,
        ...options
      };

      setNotifications(prev => [...prev, notification]);

      // 自动移除（如果设置了duration）
      if (duration > 0) {
        setTimeout(() => {
          hideNotification(id);
        }, duration);
      }

      return id;
    },
    [generateId]
  );

  // 隐藏通知
  const hideNotification = useCallback(id => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, open: false } : notification
      )
    );

    // 延迟移除以允许动画完成
    setTimeout(() => {
      setNotifications(prev => prev.filter(notification => notification.id !== id));
    }, 300);
  }, []);

  // 显示对话框
  const showDialog = useCallback(
    (type, options = {}) => {
      const id = generateId();

      const dialog = {
        id,
        type,
        open: true,
        title: options.title || '确认',
        message: options.message || '',
        content: options.content,
        onConfirm: options.onConfirm,
        onCancel: options.onCancel,
        confirmText: options.confirmText || '确认',
        cancelText: options.cancelText || '取消',
        showCancel: options.showCancel !== false,
        ...options
      };

      setDialogs(prev => [...prev, dialog]);
      return id;
    },
    [generateId]
  );

  // 隐藏对话框
  const hideDialog = useCallback(id => {
    setDialogs(prev =>
      prev.map(dialog => (dialog.id === id ? { ...dialog, open: false } : dialog))
    );

    setTimeout(() => {
      setDialogs(prev => prev.filter(dialog => dialog.id !== id));
    }, 300);
  }, []);

  // 显示Toast
  const showToast = useCallback(
    (message, type = 'info', duration) => {
      const id = generateId();
      const toastDuration = duration || AUTO_HIDE_DURATIONS.toast;

      const toast = {
        id,
        message,
        type,
        visible: true
      };

      setToasts(prev => [...prev, toast]);

      setTimeout(() => {
        setToasts(prev => prev.filter(t => t.id !== id));
      }, toastDuration);

      return id;
    },
    [generateId]
  );

  // 便捷方法
  const success = useCallback(
    (message, options) => showNotification('success', message, options),
    [showNotification]
  );

  const error = useCallback(
    (message, options) => showNotification('error', message, options),
    [showNotification]
  );

  const warning = useCallback(
    (message, options) => showNotification('warning', message, options),
    [showNotification]
  );

  const info = useCallback(
    (message, options) => showNotification('info', message, options),
    [showNotification]
  );

  const confirm = useCallback(
    (message, options) => showDialog('confirm', { message, ...options }),
    [showDialog]
  );

  const toast = useCallback(
    (message, type, duration) => showToast(message, type, duration),
    [showToast]
  );

  const value = {
    // 基础方法
    showNotification,
    hideNotification,
    showDialog,
    hideDialog,
    showToast,

    // 便捷方法
    success,
    error,
    warning,
    info,
    confirm,
    toast
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}

      {/* 渲染通知 */}
      {notifications.map(notification => (
        <NotificationRenderer
          key={notification.id}
          notification={notification}
          onClose={() => hideNotification(notification.id)}
        />
      ))}

      {/* 渲染对话框 */}
      {dialogs.map(dialog => (
        <DialogRenderer key={dialog.id} dialog={dialog} onClose={() => hideDialog(dialog.id)} />
      ))}

      {/* 渲染Toast */}
      {toasts.map(toast => (
        <ToastRenderer key={toast.id} toast={toast} />
      ))}
    </NotificationContext.Provider>
  );
};

// 通知渲染组件
const NotificationRenderer = ({ notification, onClose }) => {
  const getIcon = type => {
    switch (type) {
      case 'success':
        return <SuccessIcon />;
      case 'error':
        return <ErrorIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'info':
        return <InfoIcon />;
      default:
        return <InfoIcon />;
    }
  };

  return (
    <CenteredSnackbar
      open={notification.open}
      autoHideDuration={notification.duration > 0 ? notification.duration : null}
      onClose={onClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      TransitionComponent={Slide}
      TransitionProps={{ direction: 'down' }}
    >
      <Alert
        onClose={onClose}
        severity={notification.type}
        variant="filled"
        icon={getIcon(notification.type)}
        sx={{
          minWidth: '300px',
          maxWidth: '500px',
          fontSize: '14px',
          '& .MuiAlert-message': {
            display: 'flex',
            flexDirection: 'column',
            gap: 0.5
          }
        }}
      >
        {notification.title && (
          <Typography variant="subtitle2" component="div" sx={{ fontWeight: 600 }}>
            {notification.title}
          </Typography>
        )}
        <Typography variant="body2" component="div">
          {notification.message}
        </Typography>
      </Alert>
    </CenteredSnackbar>
  );
};

// 对话框渲染组件
const DialogRenderer = ({ dialog, onClose }) => {
  const handleConfirm = () => {
    if (dialog.onConfirm) {
      dialog.onConfirm();
    }
    onClose();
  };

  const handleCancel = () => {
    if (dialog.onCancel) {
      dialog.onCancel();
    }
    onClose();
  };

  const getIcon = type => {
    switch (type) {
      case 'confirm':
        return <WarningIcon color="warning" sx={{ fontSize: 48 }} />;
      case 'delete':
        return <ErrorIcon color="error" sx={{ fontSize: 48 }} />;
      case 'save':
        return <SuccessIcon color="success" sx={{ fontSize: 48 }} />;
      default:
        return <InfoIcon color="info" sx={{ fontSize: 48 }} />;
    }
  };

  return (
    <CenteredDialog
      open={dialog.open}
      onClose={handleCancel}
      maxWidth="sm"
      fullWidth
      TransitionComponent={Fade}
      TransitionProps={{ timeout: 300 }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center" gap={2}>
          {getIcon(dialog.type)}
          <Typography variant="h6" component="div">
            {dialog.title}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        {dialog.content ? (
          dialog.content
        ) : (
          <Typography variant="body1" color="text.secondary">
            {dialog.message}
          </Typography>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        {dialog.showCancel && (
          <Button onClick={handleCancel} variant="outlined" color="inherit" sx={{ mr: 1 }}>
            {dialog.cancelText}
          </Button>
        )}
        <Button
          onClick={handleConfirm}
          variant="contained"
          color={dialog.type === 'delete' ? 'error' : 'primary'}
          autoFocus
        >
          {dialog.confirmText}
        </Button>
      </DialogActions>
    </CenteredDialog>
  );
};

// Toast渲染组件
const ToastRenderer = ({ toast }) => {
  const getIcon = type => {
    switch (type) {
      case 'success':
        return <SuccessIcon sx={{ fontSize: 20 }} />;
      case 'error':
        return <ErrorIcon sx={{ fontSize: 20 }} />;
      case 'warning':
        return <WarningIcon sx={{ fontSize: 20 }} />;
      case 'info':
        return <InfoIcon sx={{ fontSize: 20 }} />;
      default:
        return <InfoIcon sx={{ fontSize: 20 }} />;
    }
  };

  if (!toast.visible) {
    return null;
  }

  return (
    <ToastContainer severity={toast.type}>
      {getIcon(toast.type)}
      <Typography variant="body2" sx={{ flex: 1 }}>
        {toast.message}
      </Typography>
    </ToastContainer>
  );
};

// Hook for using notifications
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

// PropTypes
NotificationProvider.propTypes = {
  children: PropTypes.node.isRequired
};

NotificationRenderer.propTypes = {
  notification: PropTypes.object.isRequired,
  onClose: PropTypes.func.isRequired
};

DialogRenderer.propTypes = {
  dialog: PropTypes.object.isRequired,
  onClose: PropTypes.func.isRequired
};

ToastRenderer.propTypes = {
  toast: PropTypes.object.isRequired
};

export default NotificationProvider;
