import React from 'react';
import PropTypes from 'prop-types';
import { Box, Grid, Paper, Typography, Card, CardContent } from '@mui/material';
import StandardPageLayout from './StandardPageLayout';
import {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions
} from 'constants/styleConstants';

/**
 * DashboardPageLayout - 仪表板页面布局组件
 *
 * 专为仪表板/统计页面优化的布局，提供：
 * - 筛选控制区域
 * - 关键指标卡片网格
 * - 图表展示区域
 * - 数据表格区域
 * - 响应式网格布局
 * - 小部件区域
 */
const DashboardPageLayout = ({
  title,
  subtitle,
  filterSection,
  metrics = [],
  charts = [],
  tables = [],
  widgets = [],
  actions,
  maxWidth = 'xl',
  spacing: customSpacing = 'normal',
  gridSpacing = 3,
  ...props
}) => {
  // 仪表板专用样式
  const dashboardStyles = {
    filterSection: {
      marginBottom: spacing['2xl'],
      padding: spacing.lg,
      borderRadius: dimensions.borderRadius.base,
      backgroundColor: colors.background.light,
      boxShadow: shadows.sm
    },
    metricsGrid: {
      marginBottom: spacing['3xl']
    },
    metricCard: {
      height: '100%',
      borderRadius: dimensions.borderRadius.base,
      boxShadow: shadows.card,
      transition: transitions.all,
      '&:hover': {
        boxShadow: shadows.hover,
        transform: 'translateY(-2px)'
      }
    },
    metricContent: {
      padding: spacing.lg,
      textAlign: 'center'
    },
    metricValue: {
      fontSize: typography.fontSize['3xl'],
      fontWeight: typography.fontWeight.bold,
      color: colors.primary.main,
      marginBottom: spacing.xs
    },
    metricLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.xs
    },
    metricChange: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.medium
    },
    metricChangePositive: {
      color: colors.success.main
    },
    metricChangeNegative: {
      color: colors.error.main
    },
    metricChangeNeutral: {
      color: colors.text.secondary
    },
    chartSection: {
      marginBottom: spacing['3xl']
    },
    chartContainer: {
      padding: spacing.lg,
      borderRadius: dimensions.borderRadius.base,
      backgroundColor: colors.background.paper,
      boxShadow: shadows.card,
      height: '400px',
      display: 'flex',
      flexDirection: 'column'
    },
    chartTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.md
    },
    chartContent: {
      flex: 1,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    },
    tableSection: {
      marginBottom: spacing['3xl']
    },
    tableContainer: {
      padding: spacing.lg,
      borderRadius: dimensions.borderRadius.base,
      backgroundColor: colors.background.paper,
      boxShadow: shadows.card
    },
    tableTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.md,
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    widgetGrid: {
      marginBottom: spacing['2xl']
    },
    widget: {
      padding: spacing.lg,
      borderRadius: dimensions.borderRadius.base,
      backgroundColor: colors.background.paper,
      boxShadow: shadows.card,
      height: '100%'
    },
    emptyState: {
      textAlign: 'center',
      padding: spacing['4xl'],
      color: colors.text.secondary
    }
  };

  // 渲染指标卡片
  const renderMetrics = () => {
    if (metrics.length === 0) {
      return null;
    }

    return (
      <Box sx={dashboardStyles.metricsGrid}>
        <Grid container spacing={gridSpacing}>
          {metrics.map((metric, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
              <Card sx={dashboardStyles.metricCard}>
                <CardContent sx={dashboardStyles.metricContent}>
                  <Typography variant="h3" sx={dashboardStyles.metricValue}>
                    {metric.value}
                  </Typography>
                  <Typography variant="body2" sx={dashboardStyles.metricLabel}>
                    {metric.label}
                  </Typography>
                  {metric.change && (
                    <Typography
                      variant="caption"
                      sx={{
                        ...dashboardStyles.metricChange,
                        ...(metric.changeType === 'positive'
                          ? dashboardStyles.metricChangePositive
                          : metric.changeType === 'negative'
                          ? dashboardStyles.metricChangeNegative
                          : dashboardStyles.metricChangeNeutral)
                      }}
                    >
                      {metric.changeType === 'positive' && '+'}
                      {metric.change}
                      {metric.changeLabel && ` ${metric.changeLabel}`}
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  // 渲染图表区域
  const renderCharts = () => {
    if (charts.length === 0) {
      return null;
    }

    return (
      <Box sx={dashboardStyles.chartSection}>
        <Grid container spacing={gridSpacing}>
          {charts.map((chart, index) => (
            <Grid item xs={12} md={chart.width || 6} key={index}>
              <Box sx={dashboardStyles.chartContainer}>
                <Typography variant="h6" sx={dashboardStyles.chartTitle}>
                  {chart.title}
                  {chart.actions && (
                    <Box sx={{ display: 'flex', gap: spacing.sm }}>{chart.actions}</Box>
                  )}
                </Typography>
                <Box sx={dashboardStyles.chartContent}>
                  {chart.content || (
                    <Typography sx={dashboardStyles.emptyState}>暂无图表数据</Typography>
                  )}
                </Box>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  // 渲染数据表格区域
  const renderTables = () => {
    if (tables.length === 0) {
      return null;
    }

    return (
      <Box sx={dashboardStyles.tableSection}>
        <Grid container spacing={gridSpacing}>
          {tables.map((table, index) => (
            <Grid item xs={12} md={table.width || 12} key={index}>
              <Box sx={dashboardStyles.tableContainer}>
                <Box sx={dashboardStyles.tableTitle}>
                  <Typography variant="h6">{table.title}</Typography>
                  {table.actions && (
                    <Box sx={{ display: 'flex', gap: spacing.sm }}>{table.actions}</Box>
                  )}
                </Box>
                {table.content || (
                  <Typography sx={dashboardStyles.emptyState}>暂无表格数据</Typography>
                )}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  // 渲染小部件区域
  const renderWidgets = () => {
    if (widgets.length === 0) {
      return null;
    }

    return (
      <Box sx={dashboardStyles.widgetGrid}>
        <Grid container spacing={gridSpacing}>
          {widgets.map((widget, index) => (
            <Grid item xs={12} sm={widget.width || 6} md={widget.mdWidth || 4} key={index}>
              <Box sx={dashboardStyles.widget}>
                {widget.title && (
                  <Typography variant="h6" sx={{ marginBottom: spacing.md }}>
                    {widget.title}
                  </Typography>
                )}
                {widget.content || (
                  <Typography sx={dashboardStyles.emptyState}>暂无小部件数据</Typography>
                )}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  return (
    <StandardPageLayout
      title={title}
      subtitle={subtitle}
      actions={actions}
      maxWidth={maxWidth}
      spacing={customSpacing}
      showPaper={false}
      {...props}
    >
      {/* 筛选控制区域 */}
      {filterSection && <Box sx={dashboardStyles.filterSection}>{filterSection}</Box>}

      {/* 关键指标区域 */}
      {renderMetrics()}

      {/* 图表区域 */}
      {renderCharts()}

      {/* 数据表格区域 */}
      {renderTables()}

      {/* 小部件区域 */}
      {renderWidgets()}
    </StandardPageLayout>
  );
};

DashboardPageLayout.propTypes = {
  // 页面标题
  title: PropTypes.string,
  // 副标题
  subtitle: PropTypes.string,
  // 筛选控制区域
  filterSection: PropTypes.node,
  // 关键指标配置
  metrics: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      label: PropTypes.string.isRequired,
      change: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      changeType: PropTypes.oneOf(['positive', 'negative', 'neutral']),
      changeLabel: PropTypes.string
    })
  ),
  // 图表配置
  charts: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      content: PropTypes.node,
      actions: PropTypes.node,
      width: PropTypes.number // Grid width (1-12)
    })
  ),
  // 数据表格配置
  tables: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      content: PropTypes.node,
      actions: PropTypes.node,
      width: PropTypes.number // Grid width (1-12)
    })
  ),
  // 小部件配置
  widgets: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      content: PropTypes.node,
      width: PropTypes.number, // sm breakpoint width
      mdWidth: PropTypes.number // md breakpoint width
    })
  ),
  // 顶部操作按钮
  actions: PropTypes.node,
  // 容器最大宽度
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]),
  // 间距类型
  spacing: PropTypes.oneOf(['compact', 'normal', 'comfortable']),
  // 网格间距
  gridSpacing: PropTypes.number
};

export default DashboardPageLayout;
