/**
 * 自动确认系统演示
 * 展示如何在现有代码中集成自动确认功能
 */

import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Alert,
  Divider,
  Grid,
  Paper,
  Stack
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Security as SecurityIcon,
  BatchPrediction as BatchIcon,
  Settings as SettingsIcon,
  PlayArrow as PlayIcon
} from '@mui/icons-material';
import { smartConfirm, confirmWithPreset } from '../utils/confirmReplacer';

const AutoConfirmDemo = () => {
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { message, type, timestamp }]);
  };

  // 演示1: 传统的 confirm() 替换
  const handleTraditionalConfirm = async () => {
    try {
      // 原来的代码: if (!confirm('确定要删除这条记录吗？')) return;
      // 现在的代码:
      const confirmed = await smartConfirm('确定要删除这条记录吗？');
      if (!confirmed) {
        addLog('用户取消了删除操作', 'warning');
        return;
      }
      addLog('删除操作已确认，开始执行删除...', 'success');
    } catch (error) {
      addLog(`操作失败: ${error.message}`, 'error');
    }
  };

  // 演示2: 带预设的确认
  const handlePresetConfirm = async () => {
    try {
      const confirmed = await confirmWithPreset(
        '确定要撤销该用户的权限吗？此操作不可逆！',
        'revokePermission'
      );
      if (!confirmed) {
        addLog('权限撤销操作被取消', 'warning');
        return;
      }
      addLog('权限撤销已确认，正在处理...', 'success');
    } catch (error) {
      addLog(`权限操作失败: ${error.message}`, 'error');
    }
  };

  // 演示3: 批量操作确认
  const handleBatchOperation = async () => {
    try {
      const confirmed = await confirmWithPreset(
        '即将批量删除 156 条记录，此操作不可撤销！',
        'batchDelete'
      );
      if (!confirmed) {
        addLog('批量删除操作被取消', 'warning');
        return;
      }
      addLog('批量删除已确认，开始处理...', 'success');

      // 模拟批量处理
      for (let i = 1; i <= 5; i++) {
        setTimeout(() => {
          addLog(`正在处理第 ${i}/5 批数据...`, 'info');
        }, i * 500);
      }
    } catch (error) {
      addLog(`批量操作失败: ${error.message}`, 'error');
    }
  };

  // 演示4: 同步版本（兼容现有代码结构）
  const handleSyncConfirm = () => {
    try {
      // 使用同步版本，无需改变函数结构
      if (!smartConfirm('确定要导出所有财务数据吗？', { async: false })) {
        addLog('数据导出操作被取消', 'warning');
        return;
      }
      addLog('数据导出已确认，开始生成报表...', 'success');
    } catch (error) {
      addLog(`导出失败: ${error.message}`, 'error');
    }
  };

  // 打开命令界面
  const openCommandInterface = () => {
    if (window.autoCommand) {
      window.autoCommand();
      addLog('命令界面已打开（或按 Ctrl+Shift+/）', 'info');
    } else {
      addLog('命令界面尚未加载，请刷新页面', 'error');
    }
  };

  // 快速切换自动确认
  const quickToggle = () => {
    if (window.toggleAuto) {
      const enabled = window.toggleAuto();
      addLog(`自动确认已${enabled ? '启用' : '禁用'}`, enabled ? 'success' : 'info');
    } else {
      addLog('自动确认控制器尚未加载，请刷新页面', 'error');
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        🤖 自动确认系统演示
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        这个演示展示了如何在现有项目中集成自动确认系统。通过命令界面可以快速切换自动化模式。
      </Alert>

      <Grid container spacing={3}>
        {/* 控制面板 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🎮 控制面板
              </Typography>

              <Stack spacing={2}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<SettingsIcon />}
                  onClick={openCommandInterface}
                  fullWidth
                >
                  打开命令界面
                </Button>

                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<PlayIcon />}
                  onClick={quickToggle}
                  fullWidth
                >
                  快速切换自动确认
                </Button>

                <Typography variant="body2" color="text.secondary">
                  💡 提示: 按 <code>Ctrl+Shift+/</code> 快速打开命令界面
                </Typography>

                <Divider />

                <Typography variant="subtitle2" gutterBottom>
                  📋 可用命令:
                </Typography>
                <Box component="ul" sx={{ pl: 2, margin: 0 }}>
                  <li>
                    <code>/auto-confirm</code> - 启用/禁用自动确认
                  </li>
                  <li>
                    <code>/auto-mode</code> - 选择自动化模式
                  </li>
                  <li>
                    <code>/toggle</code> - 快速切换状态
                  </li>
                  <li>
                    <code>/status</code> - 查看当前状态
                  </li>
                  <li>
                    <code>/help</code> - 显示帮助信息
                  </li>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* 演示操作 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🧪 演示操作
              </Typography>

              <Stack spacing={2}>
                <Button
                  variant="contained"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={handleTraditionalConfirm}
                  fullWidth
                >
                  传统确认替换演示
                </Button>

                <Button
                  variant="contained"
                  color="warning"
                  startIcon={<SecurityIcon />}
                  onClick={handlePresetConfirm}
                  fullWidth
                >
                  权限操作确认演示
                </Button>

                <Button
                  variant="contained"
                  color="info"
                  startIcon={<BatchIcon />}
                  onClick={handleBatchOperation}
                  fullWidth
                >
                  批量操作确认演示
                </Button>

                <Button variant="outlined" onClick={handleSyncConfirm} fullWidth>
                  同步确认演示（兼容模式）
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* 操作日志 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">📝 操作日志</Typography>
                <Button size="small" onClick={clearLogs}>
                  清空日志
                </Button>
              </Box>

              <Paper
                sx={{
                  maxHeight: 300,
                  overflow: 'auto',
                  p: 2,
                  backgroundColor: '#f5f5f5',
                  fontFamily: 'monospace'
                }}
              >
                {logs.length === 0 ? (
                  <Typography color="text.secondary" style={{ fontStyle: 'italic' }}>
                    暂无操作日志，点击上方按钮开始演示...
                  </Typography>
                ) : (
                  logs.map((log, index) => (
                    <Box key={index} mb={1}>
                      <Typography
                        variant="body2"
                        sx={{
                          color:
                            log.type === 'success'
                              ? 'green'
                              : log.type === 'warning'
                              ? 'orange'
                              : log.type === 'error'
                              ? 'red'
                              : 'inherit'
                        }}
                      >
                        <span style={{ color: '#666' }}>[{log.timestamp}]</span> {log.message}
                      </Typography>
                    </Box>
                  ))
                )}
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* 使用说明 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                📖 使用说明
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>1. 开启自动确认:</strong> 按 <code>Ctrl+Shift+/</code> 打开命令界面，选择{' '}
                <code>/auto-confirm</code> → <code>1. 是，启用自动确认</code>
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>2. 选择模式:</strong> 使用 <code>/auto-mode</code>{' '}
                命令选择合适的自动化模式：
                <br />• <strong>开发模式</strong>: 快速自动确认，延迟200ms
                <br />• <strong>安全模式</strong>: 谨慎自动确认，危险操作默认拒绝
                <br />• <strong>演示模式</strong>: 延迟1.5秒，便于观察过程
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>3. 集成到现有代码:</strong> 将 <code>confirm()</code> 替换为{' '}
                <code>smartConfirm()</code>，支持异步和同步两种模式
              </Typography>

              <Typography variant="body2">
                <strong>4. 快速切换:</strong> 在控制台输入 <code>toggleAuto()</code>{' '}
                或使用命令界面的 <code>/toggle</code> 命令
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AutoConfirmDemo;
